'use client';
import React, { useEffect, useState } from 'react';
import { usePermission } from '@benzinga/user-context';
import { Impression } from '@benzinga/analytics';

const variants = {
  crypto: '/next-assets/images/banners/edge/market-page/crypto-cta.png',
  etf: '/next-assets/images/banners/edge/market-page/etf-cta.png',
  market: '/next-assets/images/banners/edge/market-page/market-cta.png',
  ranking: '/next-assets/images/banners/edge/market-page/rankings-cta.png',
};

export type EdgeCTAVariant = keyof typeof variants;

export const EdgeCTA = ({ variant }: { variant: EdgeCTAVariant }) => {
  const imageSrc = variants[variant ?? 'ranking'];
  const url = `https://www.benzinga.com/premium/ideas/benzinga-edge/?utm_campaign=marketpage&utm_adType=marketpagead&utm_ad=${variant}`;
  const hasBzEdge = usePermission('com/read', 'unlimited-calendars');
  const [show, setShow] = useState(true);

  useEffect(() => {
    if (hasBzEdge) {
      setShow(false);
    }
  }, [hasBzEdge]);

  if (!show) return null;

  return (
    <Impression campaign_id="marketpage" unit_type={variant}>
      <a
        className="w-full h-full rounded-md flex items-center justify-center"
        href={url}
        rel="noopener noreferrer"
        target="_blank"
      >
        <img alt="Benzinga Edge CTA Banner" className="mt-2" object-fit="contain" src={imageSrc}></img>
      </a>
    </Impression>
  );
};
