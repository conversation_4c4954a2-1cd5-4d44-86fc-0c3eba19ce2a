# v4.12.0

## Bug
  

## Task
  - add campaign event view tracking to all ads on the markets page (BZ)
    [BZ-13295](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13295)
  - use change and change from the quote api (BZ)
    [BZ-13324](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13324)
  - move raptive content 1 higher by one paragraph (BZ)
    [BZ-13325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13325)
  - july 4 sale banners (BZ)
    [BZ-13398](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13398)
  - bump notification box fully below the ad banner (BZ)
    [BZ-13399](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13399)
  - change ring the bell description in email newsletter page (BZ)
    [BZ-13401](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13401)

# v4.11.0

## Task
  - change sponsored label in feeds and author pages under headline to partner content or (BZ)
    [BZ-13304](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13304)
  - move products data information to somewhere shareable by web and mobile app (BZ)
    [BZ-13368](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13368)
  - trigger notification box at 10 scroll depth (BZ)
    [BZ-13370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13370)
  - set image size for facebook logo in the footer (BZ)
    [BZ-13373](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13373)
  - update preload prefetch in ad stack (BZ)
    [BZ-13387](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13387)
  - updates to the free newsletters page (BZ)
    [BZ-13392](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13392)

# v4.10.0

## Feature
  - erx gaps ad unit on services page (BZ)
    [BZ-13381](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13381)

## Bug
  - fix section not populating correctly on slug route pages (BZ)
    [BZ-13365](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13365)

## Task
  - add campaign event view tracking to these ads on the quote page (BZ)
    [BZ-13296](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13296)
  - change sponsored label in feeds and author pages under headline to partner content or (BZ)
    [BZ-13304](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13304)
  - update webinar ad spot on services page (BZ)
    [BZ-13338](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13338)
  - change zinger key points header to either h2 or some non header markup (BZ)
    [BZ-13375](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13375)
  - clean up stacked cta slider block ui (BZ)
    [BZ-13377](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13377)
  - remove markets cannabis from nav (BZ)
    [BZ-13379](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13379)
  - add webinar banner (BZ)
    [BZ-13386](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13386)
  - uncomment some google script calls (BZ)
    [BZ-13388](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13388)

# v4.9.0

## Task
  - add campaign event view tracking to this ad on benzinga newsdesk content (BZ)
    [BZ-13297](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13297)
  - add campaign event view tracking to this ad in the new account flow (BZ)
    [BZ-13299](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13299)
  - add campaign event view tracking to this unit on article pages (BZ)
    [BZ-13300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13300)
  - enable sophi on production (BZ)
    [BZ-13362](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13362)

# v4.8.0

## Bug
  - add request on the client-side as a fallback if followup questions for article are not available server-side (BZ)
    [BZ-13309](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13309)

## Task
  - add campaign event view tracking to this ad on the services page (BZ)
    [BZ-13293](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13293)
  - update webinar banners (BZ)
    [BZ-13363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13363)

# v4.7.0

## Feature
  - sophi integration testing (BZ)
    [BZ-13086](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13086)

## Bug
  - remove server side user checks on account page (BZ)
    [BZ-13340](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13340)
  - fix cancel subscription button display (BZ)
    [BZ-13342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13342)
  - fix type error for api market on ranking tables 2 (BZ)
    [BZ-13344](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13344)
  - fix floating wnstn widget header z index issues (BZ)
    [BZ-13346](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13346)
  - remove callback params from wnstn widget and add logging for wnstn followup questions response (BZ)
    [BZ-13348](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13348)
  - fix webinar block url fallbacks (BZ)
    [BZ-13354](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13354)

## Task
  - pagespeed and ad optimization (BZ)
    [BZ-12962](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12962)
  - on articlepage apply fetchpriority high to primary image to improve pagespeed (BZ)
    [BZ-13288](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13288)
  - desktop ad optimizations (BZ)
    [BZ-13316](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13316)
  - remove next benzinga com references in quote page meta (BZ)
    [BZ-13334](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13334)
  - update webinar ad spot on services page (BZ)
    [BZ-13338](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13338)
  - detect when raptive ad footer is visible and adjust wnstn floating widget positioning (BZ)
    [BZ-13341](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13341)
  - fix wnstn display issues (BZ)
    [BZ-13349](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13349)
  - add sophi tracking to app router pages (BZ)
    [BZ-13350](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13350)
  - last call webinar updates (BZ)
    [BZ-13356](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13356)
  - load video player at the bottom of articles if not loaded already (BZ)
    [BZ-13358](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13358)

# v4.6.0

## Bug
  - typeerror cannot read properties of undefined reading lasttradeprice (BZ)
    [BZ-13330](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13330)
  - antd popconfirm on account billing page is causing finddomnode is not a function error (BZ)
    [BZ-13337](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13337)

## Task
  - add webinar banners pattern trader pro (BZ)
    [BZ-13276](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13276)
  - add wnstn to the homepage (BZ)
    [BZ-13308](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13308)
  - nativo optimizations (BZ)
    [BZ-13317](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13317)
  - move raptive content 1 higher by one paragraph (BZ)
    [BZ-13325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13325)
  - fix hydration cls issues from nextjs update (BZ)
    [BZ-13329](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13329)

# v4.5.0

## Feature
  

## Bug
  - potential z index issue with wnstn (BZ)
    [BZ-13311](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13311)

## Task
  - lazy load footer footer images (BZ)
    [BZ-13289](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13289)
  - update wnstn to use v2 endpoint (BZ)
    [BZ-13291](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13291)
  - allow wnstn floating widget to appear even when user isn t logged in (BZ)
    [BZ-13303](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13303)

# v4.4.0

## Feature
  - implement floating wnstn widget on benzinga com (BZ)
    [BZ-13155](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13155)

## Task
  - add webinar banner (BZ)
    [BZ-13274](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13274)
  - tabnabbing concerns (BZ)
    [BZ-13281](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13281)
  - wnstn floating widget code cleanup (BZ)
    [BZ-13287](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13287)
  - update endpoints and tracking event action for wnstn widget (BZ)
    [BZ-13290](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13290)
  - update paywall events to match utm parameters like we do for other campaign impressions (BZ)
    [BZ-13254](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13254)

# v4.3.0

## Bug
  - error fetching account settings referenceerror window is not defined (BZ)
    [BZ-13272](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13272)
  - some container widths missing on quote pages (BZ)
    [BZ-13275](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13275)
  - missing taboola on article pages (BZ)
    [BZ-13277](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13277)

## Task
  - add lg banner (BZ)
    [BZ-13269](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13269)

# v4.2.0

## Bug
  - article images missing on author pages 2 (BZ)
    [BZ-13266](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13266)

## Task
  - clean up mobile styling on quote page score graphics (BZ)
    [BZ-13207](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13207)
  - add raptive footer to a couple of pages on mobile web (BZ)
    [BZ-13229](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13229)
  - layout width issue for analyst trends and forecast for mobile (BZ)
    [BZ-13251](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13251)
  - remove raptive ads from partner content page (BZ)
    [BZ-13270](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13270)

# v4.1.0

## Bug
  

## Task
  - update meta for global sites (BZ)
    [BZ-13138](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13138)
  - move mdw banners edge (BZ)
    [BZ-13253](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13253)
  - hide image if there is not image available in sidebar stories (BZ)
    [BZ-13262](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13262)
  - add webinar (BZ)
    [BZ-13264](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13264)

# v4.0.0

## Bug
  

## Task
  - update next js version (BZ)
    [BZ-12334](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12334)
  - quote pages load then scroll on mobile web (BZ)
    [BZ-13234](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13234)
  - reduce ignore more coralogix errors (BZ)
    [BZ-13247](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13247)
  - add edge banner and expiry date for mdw banner (BZ)
    [BZ-13252](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13252)

# v3.77.0

## Task
  - add webinar banner and mdw banner (BZ)
    [BZ-13233](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13233)

# v3.76.0

## Bug
  

## Task
  - article page have the ui fail gracefully in cases where data is not available (BZ)
    [BZ-13177](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13177)
  - make twitter x icon white in footer (BZ)
    [BZ-13180](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13180)
  - remove middleware error syntaxerror unexpected end of json input error from being logged (BZ)
    [BZ-13221](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13221)
  - update middleware to prevent unnecessary requests (BZ)
    [BZ-13226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13226)
  - ignore more coralogix errors (BZ)
    [BZ-13230](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13230)

# v3.75.0

## Bug
  - secondary nav issue on mobile web (BZ)
    [BZ-13220](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13220)

## Task
  - dot com campaign event not logging utm data (BZ)
    [BZ-13218](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13218)
  - temp remove banners (BZ)
    [BZ-13219](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13219)

# v3.74.0

## Bug
  - fix edge ranking paywall text jankiness (BZ)
    [BZ-13203](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13203)
  - easy income portfolio paywall (BZ)
    [BZ-13206](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13206)
  - fix oversized gauge chart on quote pages when only one is displaying (BZ)
    [BZ-13211](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13211)
  - middleware error syntaxerror unexpected end of json input (BZ)
    [BZ-13216](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13216)

## Task
  - 2x mdw edge banners remove 1 lg banner (BZ)
    [BZ-13208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13208)
  - reduce ignore more coralogix errors (BZ)
    [BZ-13217](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13217)

# v3.73.0

## Bug
  - more cls issues re render (BZ)
    [BZ-13125](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13125)

## Task
  - markets page tweaks and cleanup (BZ)
    [BZ-13167](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13167)
  - add webinar banner (BZ)
    [BZ-13193](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13193)
  - remove error log error getting dividends summary ticker aeo (BZ)
    [BZ-13198](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13198)
  - reduce probe image errors (BZ)
    [BZ-13199](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13199)
  - error getting editorial preview nodeid 45399464 (BZ)
    [BZ-13200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13200)
  - bump coralogix version to 2 8 0 (BZ)
    [BZ-13201](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13201)

# v3.72.0

## Bug
  - paywall on perfect stock for edge users (BZ)
    [BZ-13178](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13178)

## Task
  - optimize rankings to reduce calls to quotes and types for market (BZ)
    [BZ-13148](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13148)

# v3.71.0

## Bug
  - warning can t perform a react state update on a component that hasn t mounted yet (BZ)
    [BZ-13157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13157)
  - add fallback for probeimage (BZ)
    [BZ-13174](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13174)

## Task
  - add campaign events for sponsored articles (BZ)
    [BZ-12859](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12859)
  - create impression events based on url parameters (BZ)
    [BZ-13098](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13098)
  - error getting dividends summary ticker xlnx (BZ)
    [BZ-13173](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13173)
  - add alternative scenario to expected placeholder count in raptiveadmanager (BZ)
    [BZ-13175](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13175)
  - add subscriber tracking to chartbeat (BZ)
    [BZ-13179](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13179)

# v3.70.0

## Bug


## Task
  - 4 add bluesky sharing option (BZ)
    [BZ-12848](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12848)
  - add financewire carousel to markets (BZ)
    [BZ-13145](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13145)
  - update webinar banner (BZ)
    [BZ-13153](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13153)
  - keep non benzinga headline only content off of the site (BZ)
    [BZ-13156](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13156)
  - revert usesubscription change (BZ)
    [BZ-13170](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13170)
  - bump taboola end of article to 70 sov (BZ)
    [BZ-13172](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13172)

# v3.69.0

## Bug
  - segment unify bad user ids being assigned to unknown users (BZ)
    [BZ-13136](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13136)
  - warning can t perform a react state update on a component that hasn t mounted yet (BZ)
    [BZ-13157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13157)

## Task
  - include ad blocks in the ssr markup - raptive (BZ)
    [BZ-13072](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13072)
  - analyst calendar disappears when switching back to the all ratings tab (BZ)
    [BZ-13107](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13107)
  - move latest news request to getmarketprops (BZ)
    [BZ-13141](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13141)
  - update robots txt (BZ)
    [BZ-13146](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13146)
  - swap lg banners (BZ)
    [BZ-13152](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13152)

# v3.68.0

## Bug
  - chart issue on markets page (BZ)
    [BZ-13137](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13137)
  - lock rankings on market page for non edge (BZ)
    [BZ-13140](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13140)

# v3.67.0

## Feature
  - new market page (BZ)
    [BZ-12865](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12865)

## Bug
  - berkshire link issue (BZ)
    [BZ-13115](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13115)
  - article page json ld error missing or in object declaration (BZ)
    [BZ-13126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13126)

# v3.66.0

## Task
  - remove amp code from fusion (BZ)
    [BZ-12930](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12930)
  - implement appropriate resource hints (BZ)
    [BZ-13076](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13076)
  - fix cls issue on quote pages (BZ)
    [BZ-13101](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13101)
  - add weds webinar (BZ)
    [BZ-13111](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13111)
  - schema improvements (BZ)
    [BZ-13119](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13119)

# v3.65.0

## Bug
  - unparsable structured data issues detected for benzinga com (BZ)
    [BZ-13097](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13097)
  - update benzinga com page pt to 350 for tsla by benchmark (BZ)
    [BZ-13110](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13110)

## Task
  - implement appropriate resource hints (BZ)
    [BZ-13076](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13076)
  - put more edge content behind the paywall (BZ)
    [BZ-13091](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13091)

# v3.64.0

## Bug
  - fix meta for calendars (BZ)
    [BZ-13105](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13105)
  - ads causing cls issue on quote pages (BZ)
    [BZ-13106](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13106)

## Task
  - banner updates (BZ)
    [BZ-13103](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13103)

# v3.63.1

## Feature


## Bug
  - url params causing raptive ads not to load (BZ)
    [BZ-13096](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13096)

## Task
  - upgade ingress nginx sandbox (BZ)
    [BZ-1985](https://gitlab.benzinga.io/benzinga/fusion/-/issues/1985)

# v3.63.0

## Bug
  - incorrect mobile header offset pre hydration calculation (BZ)
    [BZ-13087](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13087)

## Task
  - add utm source articleshare to the do not paywall list (BZ)
    [BZ-13031](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13031)
  - screener header tab not selected if additional query param in url (BZ)
    [BZ-13088](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13088)
  - app router pages have improperly formatted meta (BZ)
    [BZ-13090](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13090)

# v3.62.0

## Feature


## Bug
  - ads min js is being injected twice on mobile (BZ)
    [BZ-13070](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13070)

## Task
  - make best stocks pages paywallable (BZ)
    [BZ-12955](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12955)
  - test improve loading speed of raptive ad placements (BZ)
    [BZ-13051](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13051)
  - swap bz pro banners (BZ)
    [BZ-13058](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13058)
  - ensure parse ly is fully removed (BZ)
    [BZ-13078](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13078)
  - optimize geolocation service (BZ)
    [BZ-13079](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13079)
  - add 2 leadgen banners (BZ)
    [BZ-13081](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13081)
  - disable raptive ads on some pages (BZ)
    [BZ-13085](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13085)

# v3.61.0

## Feature


## Bug
  - fix sponsored content not showing on topic page (BZ)
    [BZ-13054](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13054)

## Task
  - improve loading speed of raptive ad placements (BZ)
    [BZ-13051](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13051)
  - add sunday webinar banner (BZ)
    [BZ-13056](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13056)

# v3.60.0

## Bug
  - blur on analyst ratings calendar is not working in conjunction with paywall (BZ)
    [BZ-13040](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13040)
  - remove capped max height from article featured tickers list (BZ)
    [BZ-13046](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13046)

## Task
  - front end work for stock alerts portfolios (BZ)
    [BZ-12588](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12588)
  - update limits in newcampaign (BZ)
    [BZ-12960](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12960)
  - add utm source articleshare to the share link and email share buttons (BZ)
    [BZ-13030](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13030)
  - remove banner (BZ)
    [BZ-13047](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13047)

# v3.59.0

## Task
  - minor footer enhancements (BZ)
    [BZ-13035](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13035)
  - add webinar (BZ)
    [BZ-13038](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13038)

# v3.58.0

## Feature


## Bug
  - allow the crypto data source in the scanner to gracefully work with the equity data source (BZ)
    [BZ-12971](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12971)
  - fix news error includes (BZ)
    [BZ-13027](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13027)

## Task
  - pagespeed and ad optimization (BZ)
    [BZ-12962](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12962)
  - new footer logos not showing up (BZ)
    [BZ-13026](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13026)
  - switch position of connatix and nativo on mobile web article pages (BZ)
    [BZ-13029](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13029)
  - register a campaign event with the action of click for article infinite scroll (BZ)
    [BZ-13032](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13032)

# v3.57.0

## Bug
  - partner content page fix (BZ)
    [BZ-12861](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12861)

## Task
  - footer clean up (BZ)
    [BZ-12864](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12864)
  - cleanup google analytics key and referrenced code (BZ)
    [BZ-12927](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12927)
  - cleanup analytics key and referenced code (BZ)
    [BZ-12928](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12928)
  - cleanup google analytics 4 measurement id (BZ)
    [BZ-12931](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12931)
  - zinger key point trigger campaign impression event (BZ)
    [BZ-13023](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13023)

# v3.56.0

## Bug
  - include missing nativo script on sponsored pages (BZ)
    [BZ-13007](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13007)
  - correct end of article taboola sov (BZ)
    [BZ-13009](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13009)

## Task
  - Improve image loading for homepage (BZ)
    [BZ-12239](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12239)
  - quote pages not updating in the premarket v2 (BZ)
    [BZ-12853](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12853)
  - remove ticker hover from mobile web (BZ)
    [BZ-12988](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12988)
  - add webinar banner and auto remove (BZ)
    [BZ-13002](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13002)
  - update article clear cache request s (BZ)
    [BZ-13003](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13003)
  - get rid of empty headings for news analyst ratings in search fields (BZ)
    [BZ-13004](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13004)
  - replace matt banners (BZ)
    [BZ-13013](https://gitlab.benzinga.io/benzinga/fusion/-/issues/13013)

# v3.55.0

## Bug


## Task
  - delay loading nativo and taboola (BZ)
    [BZ-12969](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12969)
  - move newsletter requests to com clean up moneyurl (BZ)
    [BZ-12979](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12979)
  - replace acf connatix block with acf connatix live block in article (BZ)
    [BZ-12991](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12991)
  - resolve responsive design issue on crypto edge ranking (BZ)
    [BZ-12994](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12994)
  - remove this bottom of article unit on mobile web (BZ)
    [BZ-12995](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12995)

# v3.54.0

## Bug
  - quote page isn t handling special characters in headlines (BZ)
    [BZ-12985](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12985)
  - fix staging erroring (BZ)
    [BZ-12989](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12989)

## Task
  - paywall changes (BZ)
    [BZ-12978](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12978)
  - replace turning point with matt inner circle on service page (BZ)
    [BZ-12982](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12982)
  - remove webinar add leadgen banners (BZ)
    [BZ-12984](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12984)
  - resolve issue with article rendering without ticker (BZ)
    [BZ-12987](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12987)

# v3.53.0

## Task
  - rewrite services page with app router (BZ)
    [BZ-11129](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11129)
  - new ranking display for etfs and crypto (BZ)
    [BZ-12876](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12876)
  - quote v2 (BZ)
    [BZ-12954](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12954)
  - remove optinmonster sitewide (BZ)
    [BZ-12977](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12977)
  - remove edge banners add back matt trial banner (BZ)
    [BZ-12981](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12981)

# v3.52.0

## Feature


## Bug
  - follow up from resolve fix canonical and href tags on india site (BZ)
    [BZ-12917](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12917)

## Task
  - loading flicker on topic and channel pages (BZ)
    [BZ-12805](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12805)
  - stock ranking tool tips (BZ)
    [BZ-12818](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12818)
  - add link to screener in header on top stocks pages (BZ)
    [BZ-12835](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12835)
  - quote pages not updating in the premarket (BZ)
    [BZ-12853](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12853)
  - quote page cls issue price and ad unit (BZ)
    [BZ-12901](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12901)
  - missing padding below quote menu (BZ)
    [BZ-12967](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12967)
  - add webinar (BZ)
    [BZ-12970](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12970)
  - add webinar 2 (BZ)
    [BZ-12970](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12970)

# v3.51.0

## Feature


## Task
  - ticker analytics changes (BZ)
    [BZ-12956](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12956)
  - remove webinar (BZ)
    [BZ-12961](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12961)
  - remove randomness from ticker hover (BZ)
    [BZ-12963](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12963)

# v3.50.0

## Feature


## Bug
  - remove articledata from debugging log (BZ)
    [BZ-12944](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12944)
  - campaignify not displaying on some articles (BZ)
    [BZ-12946](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12946)

## Task
  - new events for headline impressions com (BZ)
    [BZ-12796](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12796)
  - bring back end of article internal recirc widget (BZ)
    [BZ-12860](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12860)
  - add webinar banner (BZ)
    [BZ-12942](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12942)

# v3.49.0

## Feature
  - use domains from licensing admin api to insert into i frame (BZ)
    [BZ-12831](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12831)

## Task
  - keep campaignify and display units separate (BZ)
    [BZ-12935](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12935)
  - implement nativo key values pairs (BZ)
    [BZ-12939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12939)

# v3.48.0

## Feature
  - add bluesky sharing option (BZ)
    [BZ-12848](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12848)

## Bug
  - fix sorting in serversidecalendar (BZ)
    [BZ-12916](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12916)
  - more calendar cleanup (BZ)
    [BZ-12922](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12922)

## Task
  - fix topic page cls issues (BZ)
    [BZ-12806](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12806)
  - partner content in feeds (BZ)
    [BZ-12863](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12863)
  - set showsponsoredcontent to true in newstemplate (BZ)
    [BZ-12940](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12940)

# v3.47.0

## Feature
  - new quote page (BZ)
    [BZ-12847](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12847)

## Bug
  - fix server side calendar not transitioning to client side aggrid calendar (BZ)
    [BZ-12904](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12904)
  - add logs to debug editorbox being hidden sometimes (BZ)
    [BZ-12914](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12914)

## Task
  - stop concatenation of event name event action across all segment events (BZ)
    [BZ-12812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12812)
  - revert event name concatenation change and add event action property in dosegmenttrack (BZ)
    [BZ-12894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12894)
  - add webinar (BZ)
    [BZ-12895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12895)
  - apply vanity urls to route partner content (BZ)
    [BZ-12903](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12903)
  - rollback event name concatenation change removal of    (BZ)
    [BZ-12905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12905)
  - remove webinar banner (BZ)
    [BZ-12907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12907)
  - add 2 matt maley banners (BZ)
    [BZ-12913](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12913)

# v3.46.0

## Feature
  - more nativo functionality (BZ)
    [BZ-12770](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12770)

## Task
  - remove webinar banner mar19 (BZ)
    [BZ-12883](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12883)

# v3.45.0

## Task
  - remove parsely code from site (BZ)
    [BZ-12757](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12757)
  - add analyst accuracy on quote analyst pages (BZ)
    [BZ-12778](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12778)
  - revert edge promos to prior offers (BZ)
    [BZ-12875](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12875)
  - resolve paywall design issue on ticker hover edge ranking (BZ)
    [BZ-12878](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12878)

# v3.44.0

## Bug
  - server side rendered calendar is missing (BZ)
    [BZ-12839](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12839)
  - fix webinar block url (BZ)
    [BZ-12852](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12852)
  - error cannot destructure property ogdescription of props as it is undefined (BZ)
    [BZ-12870](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12870)
  - cls issue show raptive header banner by default (BZ)
    [BZ-12877](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12877)

## Task
  - invoke nextjs api to update header with new or updated whitelisted domains (BZ)
    [BZ-12419](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12419)
  - update edge ranking layout on quote page (BZ)
    [BZ-12809](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12809)
  - center bullets on light wallstreet desktop paywall (BZ)
    [BZ-12846](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12846)
  - add webinar banner (BZ)
    [BZ-12850](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12850)
  - update edge links on site (BZ)
    [BZ-12851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12851)
  - add screener to tools menu (BZ)
    [BZ-12869](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12869)

# v3.43.0

## Feature
  - hide images on articles greater 1 year (BZ)
    [BZ-12845](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12845)
  - hide images on articles greater 1 year2 (BZ)
    [BZ-12845](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12845)

## Task
  - topic page titles missing (BZ)
    [BZ-12707](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12707)
  - move nativo on article page to content unit 2 (BZ)
    [BZ-12804](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12804)

# v3.42.0

## Feature
  - wordpress block for webinar (BZ)
    [BZ-12687](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12687)
  - screener update links quick fix (BZ)
    [BZ-12827](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12827)

## Bug
  - cls issue on quote page (BZ)
    [BZ-12820](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12820)
  - fix rankings error tofixed (BZ)
    [BZ-12834](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12834)

## Task
  - migrate premarket page to app router (BZ)
    [BZ-11715](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11715)
  - remove webinar banner (BZ)
    [BZ-12828](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12828)
  - market crash banner edit (BZ)
    [BZ-12829](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12829)
  - fix spacing in analyst name column (BZ)
    [BZ-12833](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12833)
  - remove remaining ads in quote page header for edge users (BZ)
    [BZ-12836](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12836)

# v3.41.0

## Feature
  - top xxx stocks pages (BZ)
    [BZ-12713](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12713)
  - replace 3 edge banners with new designs (BZ)
    [BZ-12791](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12791)
  - update edge paywall for rankings launch (BZ)
    [BZ-12822](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12822)
  - benzinga spike adding screener to benzinga com next (BZ)
    [BZ-6057](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6057)

## Bug
  - issue with reports page (BZ)
    [BZ-12691](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12691)
  - fix hydration errors on quote page (BZ)
    [BZ-12819](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12819)

## Task
  - add analyst accuracy on quote analyst pages (BZ)
    [BZ-12778](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12778)
  - remove the ad on quote pages for ad light users (BZ)
    [BZ-12815](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12815)
  - add some space above the wnstn widget (BZ)
    [BZ-12816](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12816)

# v3.40.0

## Feature


## Bug
  - nativo is hiding the first pr on the quote page pr tab (BZ)
    [BZ-12706](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12706)
  - update internal endpoint for logosurl (BZ)
    [BZ-12782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12782)
  - update internal endpoint for ticker details api (BZ)
    [BZ-12783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12783)

## Task
  - use data from the quote page endpoint on the easy income portfolio (BZ)
    [BZ-12777](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12777)
  - add market moving exclusives to the do not paywall list (BZ)
    [BZ-12784](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12784)
  - remove webinar banner (BZ)
    [BZ-12788](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12788)
  - add webinar banner week mar 10 (BZ)
    [BZ-12789](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12789)
  - hide aboutquote component in sidebar on crypto quote pages (BZ)
    [BZ-12803](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12803)
  - decrease opacity on stock ranking paywalls (BZ)
    [BZ-12813](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12813)

# v3.39.0

## Feature
  - add 3 more edge banners to rotation (BZ)
    [BZ-12763](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12763)

## Bug
  - change ytdchange to ytdgainloss in sector performance heatmap (BZ)
    [BZ-12779](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12779)

## Task
  - identify all benzinga external routes that could be internal www api money (BZ)
    [BZ-11894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11894)
  - revise look of wnstn widgets (BZ)
    [BZ-12410](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12410)
  - migrate to content internal endpoint on main quote page (BZ)
    [BZ-12735](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12735)
  - remove tokensaga grandnewswire from frontend noindex nofollow logic (BZ)
    [BZ-12762](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12762)
  - add webinar banner (BZ)
    [BZ-12772](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12772)
  - about us webpage updates (BZ)
    [BZ-12780](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12780)
  - clean up runtime environment detection logic api session (BZ)
    [BZ-12781](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12781)

# v3.38.0

## Bug


## Task
  - remove global variable update runtime env variables for bz sandbox and bz beta (BZ)
    [BZ-12165](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12165)
  - add as of date to etf holdings pages (BZ)
    [BZ-12619](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12619)
  - seo updates for quote pages (BZ)
    [BZ-12664](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12664)
  - replace 2 edge banners (BZ)
    [BZ-12727](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12727)
  - remove delay for pagetrack event fix analytics reporting (BZ)
    [BZ-12753](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12753)
  - update home page blue button url (BZ)
    [BZ-12758](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12758)
  - show stock rankings independent of bulls say bears say (BZ)
    [BZ-12760](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12760)

# v3.37.0

## Feature
  - add new paywall plus add url parameters to existing (BZ)
    [BZ-12685](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12685)
  - add 3 new desktop paywalls and remove middle (BZ)
    [BZ-12708](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12708)

## Bug
  - fix nextrouter was not mounted error (BZ)
    [BZ-12716](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12716)
  - typeerror cannot read properties of undefined reading tofixed (BZ)
    [BZ-12748](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12748)

## Task
  - top of article widget (BZ)
    [BZ-12584](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12584)
  - quote page rankings update (BZ)
    [BZ-12585](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12585)
  - optimizations for raptive (BZ)
    [BZ-12681](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12681)
  - revert article page app to page router change (BZ)
    [BZ-12717](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12717)
  - change end of article taboola feed sov from 50 to 100 (BZ)
    [BZ-12718](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12718)
  - change the url of quote socket for com (BZ)
    [BZ-12719](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12719)
  - noindex nofollow 2 content types (BZ)
    [BZ-12725](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12725)
  - replace lg banner (BZ)
    [BZ-12726](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12726)
  - update promo links on services page (BZ)
    [BZ-12738](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12738)
  - rankings checkout updates (BZ)
    [BZ-12743](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12743)
  - remove newyorktech content type from being indexable (BZ)
    [BZ-12745](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12745)

# v3.36.0

## Feature
  - create sector heatmap api (BZ)
    [BZ-12702](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12702)

## Bug
  - fix segment key mapping in fusion (BZ)
    [BZ-12460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12460)
  - signals widget is not showing data when scrolling in a small version of the widget (BZ)
    [BZ-12465](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12465)
  - fix double headers 2 (BZ)
    [BZ-12684](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12684)
  - update yarn.lock (FUSION)
    [BZ-12698](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12698)

## Task
  - campaignify not displaying (BZ)
    [BZ-12646](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12646)
  - disable top non zinger point campaignify unit (BZ)
    [BZ-12683](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12683)
  - remove webinar banner (BZ)
    [BZ-12686](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12686)
  - bug on quote unusal options pages (BZ)
    [BZ-12689](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12689)
  - hide news and education tab in options (BZ)
    [BZ-12690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12690)
  - use money jobs host for beehiiv newsletter subscribe api (BZ)
    [BZ-12693](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12693)
  - append in content block right before h2 if exists (BZ)
    [BZ-12699](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12699)

# v3.35.0

## Bug
  - investigate and fix paywall behavior (BZ)
    [BZ-12674](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12674)

## Task
  - quote unusual options page (BZ)
    [BZ-12150](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12150)
  - return users to their previous page after a login (BZ)
    [BZ-12666](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12666)
  - give sponsored page a minimum height (BZ)
    [BZ-12677](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12677)
  - add webinar banner (BZ)
    [BZ-12680](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12680)
  - service page update for webinars (BZ)
    [BZ-12682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12682)

# v3.34.0

## Feature
  - hold for contract add nativo to article other pages (BZ)
    [BZ-12277](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12277)

## Bug
  - put company description back on quote page (BZ)
    [BZ-12670](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12670)
  - use data api next instead of data api for tickersurl (BZ)
    [BZ-12672](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12672)

## Task
  - replace top right rail wnstn unit with a raptive ad (BZ)
    [BZ-12653](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12653)
  - migrate data to new scanner v2 api (BZ)
    [BZ-12658](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12658)
  - save notification alert data in iam (BZ)
    [BZ-12673](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12673)
  - change field raptiveadplacements to adplacements (BZ)
    [BZ-12675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12675)

# v3.33.0

## Bug


## Task
  - when we click all rating in analyst stock ratings then the page doesn t show data and not (BZ)
    [BZ-12137](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12137)
  - migrate article page bug fixes (BZ)
    [BZ-12340](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12340)
  - remove webinar banner (BZ)
    [BZ-12625](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12625)
  - edits to about page (BZ)
    [BZ-12645](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12645)
  - remove trump 100 days banner (BZ)
    [BZ-12650](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12650)
  - change text on login page (BZ)
    [BZ-12654](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12654)

# v3.32.0

## Feature
  - new mobile paywall design (BZ)
    [BZ-12562](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12562)
  - replace 2 edge banners with new design (BZ)
    [BZ-12582](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12582)

## Bug
  - fix crypto most popular (BZ)
    [BZ-12614](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12614)
  - update wnstn widget urls (BZ)
    [BZ-12626](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12626)

## Task
  - use internal endpoint for rest v2 quotedelayed from benzinga next (BZ)
    [BZ-1024](https://gitlab.benzinga.io/benzinga/fusion/-/issues/1024)
  - add tracking for incognito pageviews (BZ)
    [BZ-12558](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12558)
  - remove from analyst mark devries name (BZ)
    [BZ-12613](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12613)
  - first name last name in my profile should not allow urls or html tags (BZ)
    [BZ-12617](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12617)
  - add webinar banner (BZ)
    [BZ-12618](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12618)
  - bump new article page template to 100 (BZ)
    [BZ-12621](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12621)
  - remove boost from main nav (BZ)
    [BZ-12628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12628)

# v3.31.0

## Feature
  - api changes next (BZ)
    [BZ-00000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/00000)

## Bug
  - fix chartbeat api data response (BZ)
    [BZ-12607](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12607)

## Task
  - remove premarket prep unit from premarket page (BZ)
    [BZ-12521](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12521)
  - move razreport podcasts from podcast page to watch page (BZ)
    [BZ-12568](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12568)
  - cache chartbeat top stories api (BZ)
    [BZ-12571](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12571)
  - remove webinar banner (BZ)
    [BZ-12580](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12580)
  - remove partner tab on com quote pages (BZ)
    [BZ-12589](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12589)
  - make api chartbeat more robust (BZ)
    [BZ-12599](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12599)
  - remove colors from top bar (BZ)
    [BZ-12600](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12600)

# v3.30.0

## Feature
  - add cancel ability to pro trialers on billing page (BZ)
    [BZ-1758](https://gitlab.benzinga.io/benzinga/fusion/-/issues/1758)

## Task
  - main nav remove cannabis (BZ)
    [BZ-12474](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12474)
  - add webinar banner (BZ)
    [BZ-12577](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12577)

# v3.29.0

## Bug
  - fix language for account page (BZ)
    [BZ-12565](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12565)

## Task
  - chartbeat doesn t fire on articles tagged why is it moving (BZ)
    [BZ-12520](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12520)
  - handle smartasset block and append script (BZ)
    [BZ-12576](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12576)

# v3.28.0

## Bug
  - signals widget is not showing data when scrolling in a small version of the widget (BZ)
    [BZ-12465](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12465)
  - fix wnstn (BZ)
    [BZ-12561](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12561)
  - fix raptive ads not showing up on markets and news stories (BZ)
    [BZ-12569](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12569)
  - hotfix wnstn fallback url (BZ)
    [BZ-12570](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12570)
  - fix wnstn prod url (BZ)
    [BZ-12572](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12572)

## Task
  - change paywall threshold to 4 free articles per week from 2 (BZ)
    [BZ-12554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12554)
  - preload main article image on new article template (BZ)
    [BZ-12567](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12567)

# v3.27.0

## Bug
  - table is causing article page to overflow (BZ)
    [BZ-12540](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12540)

## Task
  - migrate calculator pages to app router (BZ)
    [BZ-11717](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11717)
  - push notification analytics web (BZ)
    [BZ-12330](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12330)
  - use new internal editorial tools bzgo endpoint for wnstn (BZ)
    [BZ-12499](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12499)
  - make sure beehiiv registration events get to segment with email and that the email gets (BZ)
    [BZ-12526](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12526)
  - add new leadgen banner (BZ)
    [BZ-12548](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12548)
  - mediafuse finance wire remove paywall (BZ)
    [BZ-12550](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12550)
  - set height of mobile ad header units (BZ)
    [BZ-12551](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12551)
  - change paywall trigger to 40 scroll depth (BZ)
    [BZ-12553](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12553)
  - replace premarket prep with premarket playbook (BZ)
    [BZ-12556](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12556)

# v3.26.0

## Task
  - add chartbeat mab js to on homepage (BZ)
    [BZ-12479](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12479)
  - replace parsely with chartbeat for editorial sidebar widget (BZ)
    [BZ-12529](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12529)
  - bump new article page template to 80 (BZ)
    [BZ-12536](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12536)

# v3.25.0

## Feature
  - remove eoy banners and add new edge banners (BZ)
    [BZ-12505](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12505)

## Bug
  - chartbeat hostname fix (BZ)
    [BZ-12226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12226)
  - chartbeat implementation fix (BZ)
    [BZ-12226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12226)
  - wnstn widget chat is not visible on article page (BZ)
    [BZ-12434](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12434)
  - wrong quarter is being displayed for kmx (BZ)
    [BZ-12467](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12467)
  - benzinga edge hydration fix (BZ)
    [BZ-12508](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12508)
  - benzinga edge hydration fix 2 (BZ)
    [BZ-12508](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12508)
  - incontentblocks missing on article pages (BZ)
    [BZ-12523](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12523)
  - premarket embed not showing livestream due to show name change (BZ)
    [BZ-12533](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12533)
  - meta name apple mobile web app capable content yes is deprecated please include meta name (BZ)
    [BZ-12534](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12534)

## Task
  - add chartbeat to site (BZ)
    [BZ-12226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12226)
  - disable chartbeat mab script (BZ)
    [BZ-12226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12226)
  - update chartbeat referrer (BZ)
    [BZ-12226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12226)
  - add page data to wnstn asset click event (BZ)
    [BZ-12411](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12411)
  - easy income portfolio is a list of dividend payments instead of stocks (BZ)
    [BZ-12447](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12447)
  - add lead gen banner (BZ)
    [BZ-12449](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12449)
  - add issponsoredcontent check to paywall (BZ)
    [BZ-12476](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12476)
  - don t show optinmonster on sponsored content (BZ)
    [BZ-12478](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12478)
  - add issponsoredarticle check to paywall on new article template (BZ)
    [BZ-12487](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12487)
  - fix button alignment (BZ)
    [BZ-12488](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12488)
  - remove moneyline banner (BZ)
    [BZ-12501](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12501)
  - switch paywalls design back from eoy (BZ)
    [BZ-12503](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12503)
  - adjust size of ad container on premarket page (BZ)
    [BZ-12513](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12513)
  - update leadgen banner copy (BZ)
    [BZ-12514](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12514)
  - add these authors to the do not paywall list (BZ)
    [BZ-12517](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12517)
  - hide probe image errors (BZ)
    [BZ-12532](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12532)
  - revise mobile view login account menu (BZ)
    [BZ-1751](https://gitlab.benzinga.io/benzinga/fusion/-/issues/1751)

# v3.24.0

## Feature
  - test in staging fire paywall based on scroll depth of header (BZ)
    [BZ-12403](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12403)
  - replace edge banners with edge nye banners (BZ)
    [BZ-12448](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12448)
  - update paywalls to holiday design and link (BZ)
    [BZ-12472](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12472)

## Bug
  - fix segment key mapping in fusion (BZ)
    [BZ-12460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12460)

## Task
  - remove twitter script tag from article (BZ)
    [BZ-12441](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12441)
  - add partner disclosure to the new article template (BZ)
    [BZ-12442](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12442)
  - hacked content detected on https www benzinga com (BZ)
    [BZ-12453](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12453)
  - update admin tool translation v1 endpoint (BZ)
    [BZ-12459](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12459)
  - updates to key values for raptive (BZ)
    [BZ-12471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12471)

# v3.23.0

## Feature
  - update free newsletters to use beehiiv api (BZ)
    [BZ-12383](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12383)

## Bug
  - fix raptive banner placeholder showing up on mobile article pages (BZ)
    [BZ-12436](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12436)
  - fix raptive sidebar ads not loading for ad light users (BZ)
    [BZ-12452](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12452)

## Task
  - remove some raptive classes (BZ)
    [BZ-12372](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12372)
  - update paywall for gov trades (BZ)
    [BZ-12373](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12373)
  - options trade of the day paywall tag (BZ)
    [BZ-12401](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12401)
  - add slideshow support to the new article template (BZ)
    [BZ-12423](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12423)
  - remove erx banners (BZ)
    [BZ-12427](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12427)
  - remove waiting for user mouse movement before load raptive ad script (BZ)
    [BZ-12429](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12429)
  - decrease article layout header size when quote analysis is available (BZ)
    [BZ-12432](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12432)
  - replace all email capture on com and money with beehiiv (BZ)
    [BZ-12433](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12433)
  - update money api endpoints tools endpoint (BZ)
    [BZ-12437](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12437)
  - kickbox error message for newsletters (BZ)
    [BZ-12443](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12443)

# v3.22.0

## Feature
  - add erx webinar banner (BZ)
    [BZ-12415](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12415)

## Bug


## Task
  - replace trade with public link with go link (BZ)
    [BZ-12343](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12343)
  - change message for cases where companies do not provide guidance (BZ)
    [BZ-12404](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12404)
  - remove spike from ticker tape (BZ)
    [BZ-12417](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12417)
  - fix banner index (BZ)
    [BZ-12426](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12426)
  - remove sticky banner for edge users (BZ)
    [BZ-12428](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12428)

# v3.21.0

## Feature
  - campaignify bullet on new article page (BZ)
    [BZ-12375](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12375)

## Bug
  - fix race condition in raptive ad manager between setexpectedplaceholdercount and setready (BZ)
    [BZ-12388](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12388)

## Task
  - remove cyber monday banners add bz pro webinar (BZ)
    [BZ-12359](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12359)
  - add 2 new edge banners (BZ)
    [BZ-12364](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12364)
  - update money layout internal url (BZ)
    [BZ-12371](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12371)
  - campaignify end of article (BZ)
    [BZ-12376](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12376)
  - benzinga remove marketfy from benzinga com (BZ)
    [BZ-12377](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12377)
  - update m a calendar to fetch channel tag news only (BZ)
    [BZ-12408](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12408)
  - change quotes reatime socket api endpoint (BZ)
    [BZ-12412](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12412)
  - add wnstn widget on mobile version of new article template (BZ)
    [BZ-12413](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12413)
  - update service page (BZ)
    [BZ-12414](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12414)

# v3.20.0

## Feature
  - cyber monday sale banners (BZ)
    [BZ-12344](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12344)

## Bug
  - tools page has double headers and double banners (BZ)
    [BZ-12357](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12357)
  - fix price formatting on crypto quote pages and new article pages (BZ)
    [BZ-12358](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12358)

## Task
  - add the editor toolbar to the new article template (BZ)
    [BZ-12354](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12354)
  - clean up editorbox dropdown styles on new article pages (BZ)
    [BZ-12360](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12360)
  - add 12 8 webinar banner (BZ)
    [BZ-12363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12363)
  - don t show video player to edge users on ticker pages (BZ)
    [BZ-12369](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12369)
  - update paywall promo link (BZ)
    [BZ-12378](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12378)

# v3.19.0

## Feature
  - migrate short interest page to app router (BZ)
    [BZ-11716](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11716)

## Bug
  - small fixes post implement new article template (BZ)
    [BZ-12351](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12351)

## Task
  - revert black friday paywalls nov 30 (BZ)
    [BZ-12328](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12328)
  - decrease expected ad placeholder amount (BZ)
    [BZ-12352](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12352)
  - make showraptivebanner dynamic (BZ)
    [BZ-12353](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12353)
  - new article page improvements (BZ)
    [BZ-12356](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12356)

# v3.18.1

## Task
  - revert some env staging urls post implement new article template (BZ)
    [BZ-12350](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12350)

# v3.18.0

## Task
  - implement new article template (BZ)
    [BZ-11992](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11992)

# v3.17.0

## Bug
  - fix wnstn event (BZ)
    [BZ-12348](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12348)

## Task
  - remove german version of the articles (BZ)
    [BZ-12322](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12322)
  - add prwire center to whitelistedauthors (BZ)
    [BZ-12329](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12329)
  - remove redundant bz logo from banner (BZ)
    [BZ-12347](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12347)

# v3.16.0

## Feature
  - events for wnstn (BZ)
    [BZ-12316](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12316)
  - replace paywalls with black friday paywalls (BZ)
    [BZ-12332](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12332)

## Bug
  - fix mobile header offset (BZ)
    [BZ-12323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12323)
  - revert removal of slug routes post migrate article page to app router (BZ)
    [BZ-12338](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12338)
  - rollback article page app router update (BZ)
    [BZ-12342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12342)

## Task
  - update banners from early to black friday sale (BZ)
    [BZ-12325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12325)
  - remove bz pro webinar banner (BZ)
    [BZ-12327](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12327)

# v3.15.0

## Feature
  - users should be able to update their user profile (BZ)
    [BZ-10580](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10580)
  - 1 edge sale banner (BZ)
    [BZ-12308](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12308)

## Bug
  - data issues on quote pages (BZ)
    [BZ-12287](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12287)
  - fix stock report growth avg (BZ)
    [BZ-12298](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12298)
  - fix open redirect on com (BZ)
    [BZ-12300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12300)
  - make wnstn request fail by timeout (BZ)
    [BZ-12313](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12313)
  - investigate and resolve circular object to json error (BZ)
    [BZ-12319](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12319)

## Task
  - migrate article page to app router (BZ)
    [BZ-11710](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11710)
  - updates to premarket prep page (BZ)
    [BZ-12286](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12286)
  - add all to watchlist button on portfolio pages (BZ)
    [BZ-12303](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12303)
  - pro 11 26 banner (BZ)
    [BZ-12309](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12309)
  - remove 11 20 webinar banner (BZ)
    [BZ-12315](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12315)
  - fix new sale banner width (BZ)
    [BZ-12318](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12318)

# v3.14.0

## Feature
  - create a login experience for india (BZ)
    [BZ-10783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10783)
  - implement slide in widget when a user clicks on a wnstn question (BZ)
    [BZ-12255](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12255)
  - update analyst ratings earnings and unusual options paywalls (BZ)
    [BZ-12278](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12278)

## Task
  - add serverside rendering for portfolio stock tables (BZ)
    [BZ-12279](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12279)
  - add staging class to article page (BZ)
    [BZ-12295](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12295)
  - add 11 20 webinar banner (BZ)
    [BZ-12302](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12302)

# v3.13.0

## Feature
  - migrate login logout page to app router (BZ)
    [BZ-11720](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11720)
  - r spec out the cancellation flow (BZ)
    [BZ-9923](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9923)

## Bug
  - paywall not showing for non edge logged user for analyst ratings calendar (BZ)
    [BZ-12199](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12199)
  - stock reports not loading (BZ)
    [BZ-12283](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12283)

## Task
  - benzinga allow article push notification for anonymous group (BZ)
    [BZ-11978](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11978)
  - updates to stock ideas page (BZ)
    [BZ-12063](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12063)
  - chainwire financewire update on crypto and market pages (BZ)
    [BZ-12129](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12129)
  - marketbeat to quotes pages (BZ)
    [BZ-12200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12200)
  - add new tags to paywall (BZ)
    [BZ-12219](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12219)
  - updates to stock ideas page (BZ)
    [BZ-12220](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12220)
  - remove p p banner from website (BZ)
    [BZ-12271](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12271)
  - quick fix reports page symbol and search (BZ)
    [BZ-12272](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12272)
  - remove 410 for comtex feeds (BZ)
    [BZ-12291](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12291)
  - adjustable height width for the carousel news block (BZ)
    [BZ-12297](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12297)
  - make quote analysis api for technicals and fundamentals (BZ)
    [BZ-12299](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12299)

# v3.12.0

## Feature
  - remove free login option from all hard paywalls (BZ)
    [BZ-12177](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12177)
  - create middle version of wall street paywall (BZ)
    [BZ-12266](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12266)

## Task
  - quotes page all users should get realtime data (BZ)
    [BZ-11688](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11688)
  - pro handle option chains (BZ)
    [BZ-11888](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11888)
  - implement easy income portfolio page design (BZ)
    [BZ-12149](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12149)
  - quote page realtime data bug fixes (BZ)
    [BZ-12270](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12270)

# v3.11.0

## Feature
  - update free newsletters page with benzinga advisor (BZ)
    [BZ-11905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11905)
  - benzinga setup service worker for webpush (BZ)
    [BZ-11977](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11977)
  - convert header styled components to scss 2 (BZ)
    [BZ-11998](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11998)
  - update top of site banners (BZ)
    [BZ-12188](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12188)

## Bug
  - build failure fix (BZ)
    [BZ-000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/000)
  - fix raptive ad footer showing up on disabled pages (BZ)
    [BZ-12174](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12174)
  - fix top of site banner (BZ)
    [BZ-12186](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12186)
  - fix homepage cls issues (BZ)
    [BZ-12196](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12196)
  - fix option chain undefined param (BZ)
    [BZ-12213](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12213)
  - safari ios notification bug (BZ)
    [BZ-12218](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12218)
  - bz next build error undefined reading filter (BZ)
    [BZ-12227](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12227)

## Task
  - add scroll depth events on benzinga com ga4 (BZ)
    [BZ-11549](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11549)
  - inp script evaluation improvements (BZ)
    [BZ-11910](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11910)
  - fix missing column on edge analyst ratings calendar (BZ)
    [BZ-12061](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12061)
  - remove captcha from paywall version that has it shrink height of that paywall (BZ)
    [BZ-12134](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12134)
  - use consistent syntax for share classes brk a (BZ)
    [BZ-12162](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12162)
  - revert of task debug runtime env being undefined (BZ)
    [BZ-12166](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12166)
  - revert remove the paywalled body class (BZ)
    [BZ-12169](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12169)
  - update to account page subscriptions listing (BZ)
    [BZ-12175](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12175)
  - get rid of all animated benzinga logos (BZ)
    [BZ-12187](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12187)
  - remove more irrelevant error messages from coralogix (BZ)
    [BZ-12208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12208)
  - add new lead generation top of site banner (BZ)
    [BZ-12210](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12210)
  - raptive manually add session and client ids (BZ)
    [BZ-12241](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12241)
  - remove scroll event tracking and delay popup (BZ)
    [BZ-12254](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12254)
  - remove election banner from top (BZ)
    [BZ-12260](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12260)

# v3.10.0

## Feature
  - new router for about page (BZ)
    [BZ-11128](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11128)
  - top of site banner changes (BZ)
    [BZ-12133](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12133)

## Bug
  - debug runtime env being undefined (BZ)
    [BZ-11385](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11385)
  - resolve xss exploit 2 (BZ)
    [BZ-11958](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11958)
  - header disappears when a user scrolls down and then back up 2 (BZ)
    [BZ-12076](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12076)
  - post account offer bug for tech stocks (BZ)
    [BZ-12115](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12115)
  - fix article page utm check from email (BZ)
    [BZ-12116](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12116)
  - fix bottom ad height on premarket page (BZ)
    [BZ-12121](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12121)

## Task
  - benzinga handle option chains (BZ)
    [BZ-11339](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11339)
  - add tracking pixel on all sponsored content articles (BZ)
    [BZ-11851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11851)
  - fill untranslated slugs for quote (BZ)
    [BZ-11911](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11911)
  - hide campaignify on benzinga insights (BZ)
    [BZ-12052](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12052)
  - eliminate link redirects in header menus on benzinga com (BZ)
    [BZ-12053](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12053)
  - add this new content type to the paywall (BZ)
    [BZ-12119](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12119)
  - exclude chunkloaderror from being sent to coralogix (BZ)
    [BZ-12124](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12124)
  - update event tracking for b2c requirements (BZ)
    [BZ-12130](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12130)
  - remove the paywalled body class (BZ)
    [BZ-12131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12131)
  - option chain feedback fixes (BZ)
    [BZ-12139](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12139)

# v3.9.0

## Feature
  - allow users from email to see articles without a paywall (BZ)
    [BZ-12064](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12064)
  - opt in with offer after account creation (BZ)
    [BZ-12065](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12065)
  - make has access call to iam instead of subscription on page load (BZ)
    [BZ-12096](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12096)

## Bug
  - fix styles on advertiser profile quote pages (BZ)
    [BZ-12081](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12081)
  - fix home tabs spacing and mobile responsiveness (BZ)
    [BZ-12114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12114)

## Task
  - scrub site for user input dynamic urls (BZ)
    [BZ-11989](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11989)
  - prevent unnecessary money redirect api call (BZ)
    [BZ-12038](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12038)
  - merge offering comments with like dislike button (BZ)
    [BZ-12058](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12058)
  - block leadgen popups as part of ad light experience (BZ)
    [BZ-12066](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12066)
  - update raptive object value based on referrer (BZ)
    [BZ-12073](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12073)
  - fix ad targeting invalid arguments on article page (BZ)
    [BZ-12079](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12079)
  - update copy utm source for edge paywalls (BZ)
    [BZ-12087](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12087)
  - remove google one tap (BZ)
    [BZ-12098](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12098)
  - change error to coralogixrum log (BZ)
    [BZ-12099](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12099)
  - resolve commenting on alternative investment pages (BZ)
    [BZ-12107](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12107)
  - add watch tab on homepage back (BZ)
    [BZ-12111](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12111)
  - tweak logic to be less restrictive of the amount of in content article raptive ads (BZ)
    [BZ-12112](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12112)
  - integrate paywall to work with raptive and not conflict with footer ads (BZ)
    [BZ-12113](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12113)

# v3.8.0

## Feature


## Bug
  - fix rerendering for the app (BZ)
    [BZ-12056](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12056)
  - fix raptive ad inconsistent loading (BZ)
    [BZ-12068](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12068)

## Task
  - move quote page get report button next to technical and financial widgets (BZ)
    [BZ-11521](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11521)
  - replace premium research with stock ideas on homepage (BZ)
    [BZ-11681](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11681)
  - new download app banner to display inline (BZ)
    [BZ-11891](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11891)
  - use mark composite light version of the logo from our api instead of the mark light on the (BZ)
    [BZ-12057](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12057)
  - remove api videos livestream (BZ)
    [BZ-12059](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12059)

# v3.7.0

## Feature
  - paywall copy for whisper index (BZ)
    [BZ-11987](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11987)

## Bug
  - fix padding for ads on analyst ratings and earnings tabs on the quote page (BZ)
    [BZ-12027](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12027)

## Task
  - real estate split the screener and news into tabs (BZ)
    [BZ-11552](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11552)
  - update robots txt to block additional query params and sync india (BZ)
    [BZ-11918](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11918)
  - show optin monster on article pages (BZ)
    [BZ-11971](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11971)
  - tighten up sidebar space on quote pages (BZ)
    [BZ-11991](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11991)
  - clean up campaign code (BZ)
    [BZ-12015](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12015)
  - add disclosure for ai generated articles (BZ)
    [BZ-12017](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12017)
  - fix fetching videos for watch and premarket prep pages (BZ)
    [BZ-12028](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12028)
  - quick fix top stories algo remove cannabis stories (BZ)
    [BZ-12034](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12034)
  - update google one tap to pass in url (BZ)
    [BZ-12036](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12036)
  - update top of site banner (BZ)
    [BZ-12037](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12037)
  - remove fixed width from raptive in article content ad (BZ)
    [BZ-12041](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12041)

# v3.6.0

## Feature
  - move contact page to app router final (BZ)
    [BZ-11171](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11171)

## Bug
  - error nextrouter was not mounted (BZ)
    [BZ-11996](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11996)
  - fix in content raptive ads not loading on article pages (BZ)
    [BZ-12005](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12005)
  - fix livestream not showing up on premarket page referer not set (BZ)
    [BZ-12006](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12006)

## Task
  - add benzinga tv tab on home page drop down bar (BZ)
    [BZ-10858](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10858)
  - implement new design for new money topic pages (BZ)
    [BZ-11554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11554)
  - top level analyst ratings page bug v2 (BZ)
    [BZ-11682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11682)
  - add metadata for hard paywalls (BZ)
    [BZ-11686](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11686)
  - top of website banner please remove trade zero comp (BZ)
    [BZ-12000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12000)
  - lcp on quote page connatix player (BZ)
    [BZ-12001](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12001)
  - update coralogix browser package (BZ)
    [BZ-12002](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12002)
  - benzinga fix url for apis add utms (BZ)
    [BZ-12013](https://gitlab.benzinga.io/benzinga/fusion/-/issues/12013)
  - review of all next endpoints to ensure proper err codes (BZ)
    [BZ-9673](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9673)

# v3.5.0

## Feature
  - add edge upsell to google one tap flow (BZ)
    [BZ-11920](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11920)
  - chris capre top of website banner is live (BZ)
    [BZ-11959](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11959)
  - fix topic page for the insider report (BZ)
    [BZ-11966](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11966)

## Bug
  - mobile cls issues (BZ)
    [BZ-11886](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11886)
  - watch page failing to load 500 error (BZ)
    [BZ-11974](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11974)
  - fix style issues with embedded content ad on recent page (BZ)
    [BZ-11979](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11979)

## Task
  - update go clicktracker for com with node ids (BZ)
    [BZ-11335](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11335)
  - top level analyst ratings page bug (BZ)
    [BZ-11682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11682)

# v3.4.0

## Bug
  - user vijay is getting whitespace on the navigation bar and small scroll bars in com homepage (BZ)
    [BZ-11637](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11637)
  - raptive ad on recent page is not loading (BZ)
    [BZ-11880](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11880)
  - fix bacpb quote page from being unavailable (BZ)
    [BZ-11922](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11922)
  - remove any remaining reference of bztrack (BZ)
    [BZ-11932](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11932)

## Task
  - hide read more by default (BZ)
    [BZ-11969](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11969)
  - add back in content blocks in articles (BZ)
    [BZ-11972](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11972)

# v3.3.0

## Bug
  - investigate bing (BZ)
    [BZ-11862](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11862)
  - fix paywall triggering even with account (BZ)
    [BZ-11926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11926)

## Task
  - clean up sports landing pages (BZ)
    [BZ-11497](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11497)
  - define content type lang (BZ)
    [BZ-11679](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11679)
  - resolve 404 error on alternative investment platform listing pages (BZ)
    [BZ-11901](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11901)
  - please remove labor day top of website banners (BZ)
    [BZ-11938](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11938)
  - real estate email capture form (BZ)
    [BZ-11942](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11942)
  - look into article paywall and campaigns (BZ)
    [BZ-11960](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11960)
  - enable filters and search on real estate screener (BZ)
    [BZ-11961](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11961)
  - adjust paywall thresholds (BZ)
    [BZ-11963](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11963)

# v3.2.0

## Feature
  - no lead gen popups on pages displaying a paywall (BZ)
    [BZ-11782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11782)
  - labor day top of website banner (BZ)
    [BZ-11890](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11890)

## Bug
  - raptive bz ticker targeting update mei (BZ)
    [BZ-11915](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11915)

## Task
  - fix sheet update cache (BZ)
    [BZ-0000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/0000)
  - move event properties into event data (BZ)
    [BZ-11892](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11892)
  - use internal endpoint for layout requests (BZ)
    [BZ-11893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11893)
  - remove download app button (BZ)
    [BZ-11896](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11896)
  - add edge to my account dropdown (BZ)
    [BZ-11900](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11900)
  - 5050 paywall soft and hard (BZ)
    [BZ-11913](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11913)

# v3.1.0

## Feature
  - add edge upsell immediately after new account is created and before collecting demo info (BZ)
    [BZ-11832](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11832)
  - add trading competition in 3rd slot of top of site banner (BZ)
    [BZ-11877](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11877)

## Bug
  - fix segment key (BZ)
    [BZ-11861](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11861)
  - fix india segment check (BZ)
    [BZ-11873](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11873)
  - remove fixed width from bottom ad placement on premarket page (BZ)
    [BZ-11875](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11875)

## Task
  - update event tracking com (BZ)
    [BZ-11440](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11440)
  - don t show soft hard if not logged in account creation first (BZ)
    [BZ-11727](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11727)
  - don t show comment counts if 1 (BZ)
    [BZ-11791](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11791)
  - resolve comment block button ui (BZ)
    [BZ-11866](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11866)
  - add tracking to tagmanager (BZ)
    [BZ-11871](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11871)
  - fix india tracking and add page track back in (BZ)
    [BZ-11872](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11872)

# v3.0.0

## Feature
  - missing show name on podcast show page (BZ)
    [BZ-10724](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10724)
  - implement videoplayer live gutenberg block (BZ)
    [BZ-10019](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10019)
  - mobile hide menu scroll (BZ)
    [BZ-10082](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10082)
  - create delete account page (BZ)
    [BZ-10246](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10246)
  - save report pdf (BZ)
    [BZ-10320](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10320)
  - reorder and prioritize social buttons on login form (BZ)
    [BZ-10357](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10357)
  - add account cta to article homepage (BZ)
    [BZ-10504](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10504)
  - add reports link and tracking (BZ)
    [BZ-10528](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10528)
  - benzinga add comments scripts and buttons to article page (BZ)
    [BZ-10550](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10550)
  - platform level ratings do you make money how (BZ)
    [BZ-10583](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10583)
  - submit welcome flow questions on click through api (BZ)
    [BZ-10604](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10604)
  - ability to follow an author topic for email alerts (BZ)
    [BZ-10659](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10659)
  - improvements dividends pages (BZ)
    [BZ-10746](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10746)
  - updating services page (BZ)
    [BZ-10757](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10757)
  - always paywall reports page (BZ)
    [BZ-10793](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10793)
  - new taboola code for mid page placement on quotes and topic pages (BZ)
    [BZ-10811](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10811)
  - benzinga improve messsage for missing comment counts (BZ)
    [BZ-10812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10812)
  - taboola code for placement at end of quote pages (BZ)
    [BZ-10813](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10813)
  - benzinga article comments link should be clickable in infinity scroll (BZ)
    [BZ-10825](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10825)
  - add header image for pro streamlined special offer (BZ)
    [BZ-10827](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10827)
  - create improvement ad refferer block list make global (BZ)
    [BZ-10841](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10841)
  - mid page taboola placements within new sections solid ux solid rev (BZ)
    [BZ-10900](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10900)
  - mobile web should re show mobile app download (BZ)
    [BZ-10938](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10938)
  - merge earnings calendar earnings news (BZ)
    [BZ-10939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10939)
  - develop new quote page template for all global sites (BZ)
    [BZ-11019](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11019)
  - new options tab options page (BZ)
    [BZ-11023](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11023)
  - benzinga raptive integration phase 1 manual script (BZ)
    [BZ-11025](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11025)
  - add comment count in infinite scroll articles (BZ)
    [BZ-11075](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11075)
  - create contact page using app router (BZ)
    [BZ-11126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11126)
  - memorial day 2024 top of site banners (BZ)
    [BZ-11131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11131)
  - a b test soft paywall slide in pop over embedded (BZ)
    [BZ-11173](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11173)
  - new top of site banner ads for options moneyline (BZ)
    [BZ-11226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11226)
  - create new page for bad ticker searches instead of 404 (BZ)
    [BZ-11263](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11263)
  - comments on quote pages bobby bert need details (BZ)
    [BZ-11265](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11265)
  - bull vs bear on quote pages (BZ)
    [BZ-11303](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11303)
  - slideshows should be available on com (BZ)
    [BZ-11329](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11329)
  - gate bz calendars and tools for bz premium government trades (BZ)
    [BZ-11348](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11348)
  - gate bz calendars and tools for bz premium analyst calendar (BZ)
    [BZ-11353](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11353)
  - create sign up login watchlists for global template (BZ)
    [BZ-11370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11370)
  - fourth of july sale 8 new top of website banner designs (BZ)
    [BZ-11407](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11407)
  - watermark charts on report pages (BZ)
    [BZ-11425](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11425)
  - top of website banner is live (BZ)
    [BZ-11448](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11448)
  - top of website banner is live (BZ)
    [BZ-11478](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11478)
  - implement mavens marketing block on options page (BZ)
    [BZ-11489](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11489)
  - bz edge paywall (BZ)
    [BZ-11510](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11510)
  - link instances of ticker to quote pages (BZ)
    [BZ-11523](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11523)
  - place taboola invisible widget on all pages (BZ)
    [BZ-11563](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11563)
  - swap benzinga edge banners (BZ)
    [BZ-11650](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11650)
  - sheets db (BZ)
    [BZ-11664](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11664)
  - sheets db improvements (BZ)
    [BZ-11665](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11665)
  - update benzinga edge ctas (BZ)
    [BZ-11668](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11668)
  - change card form to use chargebee js iam backend (BZ)
    [BZ-11731](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11731)
  - turn on ios android push notifications for pro research (BZ)
    [BZ-9278](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9278)
  - quote page seo split earnings tables into 3 (BZ)
    [BZ-9421](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9421)
  - benzinga com infinity page title should be clickable and give the articles breathing room (BZ)
    [BZ-9719](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9719)
  - add next episode section to razreport page (BZ)
    [BZ-9971](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9971)
  - homepage refresh investing alignment (BZ)
    [BZ-9999](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9999)

## Bug
  - fix campaign content logic (BZ)
    [BZ-10031](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10031)
  - the graphs on articles appear twice (BZ)
    [BZ-10036](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10036)
  - state abbreviation is not defined bug (BZ)
    [BZ-10089](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10089)
  - ad unit is cut off on cannabis page (BZ)
    [BZ-10097](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10097)
  - topic pages throwing 5xx errors (BZ)
    [BZ-10098](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10098)
  - fix session api failing when called server side (BZ)
    [BZ-10100](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10100)
  - post fix ssr on serversidecalendar errors (BZ)
    [BZ-10101](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10101)
  - earnings surprise and the revenue surprise fields are calculated wrong on this earnings (BZ)
    [BZ-10108](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10108)
  - premarket data loading issue from prep page (BZ)
    [BZ-10109](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10109)
  - fix tickers being added to api news request when it is empty (BZ)
    [BZ-10125](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10125)
  - in article ticker widgets are redirecting to the wrong quotes pages for crypto symbols (BZ)
    [BZ-10131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10131)
  - fix load more on category pages com (BZ)
    [BZ-10133](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10133)
  - fix block inf loop (BZ)
    [BZ-10134](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10134)
  - fix blocks for sidebar (BZ)
    [BZ-10145](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10145)
  - fix ad not loading on premarket page (BZ)
    [BZ-10157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10157)
  - fix login flow directing users to welcome page (BZ)
    [BZ-10159](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10159)
  - bottom campaignify unit not firing (BZ)
    [BZ-10164](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10164)
  - fix connatix multiple schedule bug (BZ)
    [BZ-10193](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10193)
  - fix after hours quote on quote page (BZ)
    [BZ-10205](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10205)
  - get report ticker hover fix (BZ)
    [BZ-10206](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10206)
  - fix change password link in account page (BZ)
    [BZ-10209](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10209)
  - fix quicklinks margin and bullets (BZ)
    [BZ-10219](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10219)
  - fix dynamic utm for ad in calendars (BZ)
    [BZ-10224](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10224)
  - hydration issues on quote v2 page (BZ)
    [BZ-10227](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10227)
  - remove microsoft login on frontend (BZ)
    [BZ-10228](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10228)
  - benzinga connatix error uncaught syntaxerror unexpected token (BZ)
    [BZ-10231](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10231)
  - fix tickerfinder hydration issue on article page (BZ)
    [BZ-10232](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10232)
  - fix individual analyst ratings page not redirecting to analyst stock ratings when analystid (BZ)
    [BZ-10233](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10233)
  - fix alts un monetized popup (BZ)
    [BZ-10240](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10240)
  - text in ticker component is wrapping when it shouldn t be (BZ)
    [BZ-10245](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10245)
  - article rerender fix (BZ)
    [BZ-10254](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10254)
  - table in article body has incorrect display block style applied (BZ)
    [BZ-10255](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10255)
  - review submit fixes (BZ)
    [BZ-10260](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10260)
  - articles with tweets throwing hydration issues (BZ)
    [BZ-10265](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10265)
  - editor box is missing for draft articles (BZ)
    [BZ-10281](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10281)
  - fix stories not showing parse ly stats (BZ)
    [BZ-10282](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10282)
  - fix validation url for article (BZ)
    [BZ-10288](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10288)
  - image in offering card looks stretched inwards (BZ)
    [BZ-10289](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10289)
  - fix quote page (BZ)
    [BZ-10323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10323)
  - fix email autopopulation for hubspot welcome form (BZ)
    [BZ-10324](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10324)
  - fix 500 errors for article time outs (BZ)
    [BZ-10372](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10372)
  - fix quote page ad units (BZ)
    [BZ-10381](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10381)
  - update connatix video player container on quotev2 page (BZ)
    [BZ-10383](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10383)
  - small quotev2 bug fixes (BZ)
    [BZ-10385](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10385)
  - correct position of main quote tab bar on quotev2 page (BZ)
    [BZ-10394](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10394)
  - fix for topic 404 (BZ)
    [BZ-10401](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10401)
  - tab bar render issue (BZ)
    [BZ-10403](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10403)
  - reddit embed not loading on frontend fix on frontend (BZ)
    [BZ-10408](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10408)
  - amp error usage of the important css qualifier is not allowed (BZ)
    [BZ-10410](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10410)
  - benzinga issue displaying block quotes shows error something went wrong (BZ)
    [BZ-10449](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10449)
  - fix redirection on quote pages with specific d starting ticker (BZ)
    [BZ-10450](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10450)
  - news on fda calendar page doesn t have the correct news (BZ)
    [BZ-10524](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10524)
  - setnomoreresults is being incorrectly set because of incorrect condition on razreport page (BZ)
    [BZ-10525](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10525)
  - move grabbing list of support tickers to ssr (BZ)
    [BZ-10551](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10551)
  - noindex tags not working on amp (BZ)
    [BZ-10555](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10555)
  - benzinga pro link is placed under the wrong tab on the quotes page (BZ)
    [BZ-10560](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10560)
  - podcastcard has broken href (BZ)
    [BZ-10565](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10565)
  - fix article title (BZ)
    [BZ-10566](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10566)
  - tabs on com unique duplicate key error (BZ)
    [BZ-10569](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10569)
  - ticker targeting issue on quote pages (BZ)
    [BZ-10575](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10575)
  - style issue with ticker field in m a calendars (BZ)
    [BZ-10585](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10585)
  - fix quote btc usd not getting redirected to quote btc usd (BZ)
    [BZ-10601](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10601)
  - fix cls mobile issue with ticker bar height change after default text update (BZ)
    [BZ-10605](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10605)
  - fix 500 errors on quote ratings pages (BZ)
    [BZ-10618](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10618)
  - filters not working on news tab of the quotes page (BZ)
    [BZ-10623](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10623)
  - fix canonicals on quotes pages (BZ)
    [BZ-10646](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10646)
  - fix instagram embed in articles (BZ)
    [BZ-10647](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10647)
  - quote page fix time offset for charts (BZ)
    [BZ-10656](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10656)
  - fix episode show more button not working on podcasts showslug page (BZ)
    [BZ-10674](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10674)
  - fix imports for aggrid to cover quotes pages (BZ)
    [BZ-10677](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10677)
  - change bz channel ratings to earnings on the earnings page (BZ)
    [BZ-10682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10682)
  - analyst ratings don t show chronologically on quotes analyst ratings (BZ)
    [BZ-10700](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10700)
  - mobile hydration issues on home page (BZ)
    [BZ-10712](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10712)
  - server side calendar is overflowing on quote page analyst ratings tab (BZ)
    [BZ-10732](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10732)
  - fix url for podcasts in sidebar (BZ)
    [BZ-10735](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10735)
  - gov trades member redirect from card not working (BZ)
    [BZ-10744](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10744)
  - fix social login in iframe (BZ)
    [BZ-10769](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10769)
  - individual podcast page has incorrect platform links (BZ)
    [BZ-10771](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10771)
  - short interest on quotes page doesn t have a correct fallback value (BZ)
    [BZ-10782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10782)
  - ag grid styles not loading on analyst stock ratings page (BZ)
    [BZ-10805](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10805)
  - fix missing short interest scanner on short interest most shorted (BZ)
    [BZ-10806](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10806)
  - benzinga handle quote overview ratings for missing data (BZ)
    [BZ-10831](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10831)
  - fda calendar rendering issues (BZ)
    [BZ-10839](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10839)
  - small missing data and color issues on quote page (BZ)
    [BZ-10842](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10842)
  - symbols param is missing when requesting server side data for calendars (BZ)
    [BZ-10843](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10843)
  - muln quote page error (BZ)
    [BZ-10847](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10847)
  - crypto currency sol s chart is incorrectly shown on the quotes page of emeren group nyse sol (BZ)
    [BZ-10875](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10875)
  - apple podcast icon incorrectly links to google podcasts (BZ)
    [BZ-10878](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10878)
  - fix direct traffic to account page (BZ)
    [BZ-10884](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10884)
  - stop login iframe from redirecting and fix url (BZ)
    [BZ-10890](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10890)
  - cls issues with streamline banner (BZ)
    [BZ-10894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10894)
  - responsive style issue on quote page analyst ratings tab (BZ)
    [BZ-10923](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10923)
  - breaking news banner conflicting with quote tabs offset (BZ)
    [BZ-10928](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10928)
  - fix logo for margin calculator selected ticker (BZ)
    [BZ-10957](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10957)
  - padding top styling needs to be adjusted for connatix (BZ)
    [BZ-10961](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10961)
  - benzinga work through and fix short interest data tables (BZ)
    [BZ-10967](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10967)
  - story cuts off at the first reddit embed (BZ)
    [BZ-11003](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11003)
  - fix hydration errors with mobileappbannerad (BZ)
    [BZ-11009](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11009)
  - fix watchlist hover area in ticker widget (BZ)
    [BZ-11012](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11012)
  - fix country icons not rendering in globalmenu (BZ)
    [BZ-11014](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11014)
  - image for featured podcast is missing on podcast show pages (BZ)
    [BZ-11030](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11030)
  - missing header logo on crypto quote page (BZ)
    [BZ-11031](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11031)
  - fix past livestream not showing up on premarket prep page (BZ)
    [BZ-11038](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11038)
  - fix refresh for google ads (BZ)
    [BZ-11039](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11039)
  - remove coronation day logo from com (BZ)
    [BZ-11040](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11040)
  - error getting rsi ticker lpg (BZ)
    [BZ-11048](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11048)
  - cboe page is messed up (BZ)
    [BZ-11049](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11049)
  - commit hash failed to get commit hash (BZ)
    [BZ-11050](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11050)
  - amphubspot component errors warnings (BZ)
    [BZ-11052](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11052)
  - basicadbanner not loading on some calendars (BZ)
    [BZ-11074](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11074)
  - gov trades ssr errors (BZ)
    [BZ-11103](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11103)
  - issue with tablevirtualized not displaying data server side (BZ)
    [BZ-11105](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11105)
  - amp footer not styled correctly (BZ)
    [BZ-11109](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11109)
  - clicking calendar tabs on analyst stock ratings page doesn t redirect to the correct url (BZ)
    [BZ-11118](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11118)
  - fix welcome page hubspot form (BZ)
    [BZ-11125](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11125)
  - search results not displaying correctly (BZ)
    [BZ-11138](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11138)
  - fix gov trades more ssr errors (BZ)
    [BZ-11141](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11141)
  - fix follow author button size on author profile page (BZ)
    [BZ-11155](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11155)
  - fix analyst ratings analyst page calendar (BZ)
    [BZ-11169](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11169)
  - benzinga quote page fixes on mobile (BZ)
    [BZ-11186](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11186)
  - cannot read properties of null reading price change 24h (BZ)
    [BZ-11193](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11193)
  - fix replacetop for login (BZ)
    [BZ-11213](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11213)
  - fix icon styling on razreport podcast pages missing icon on search page (BZ)
    [BZ-11218](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11218)
  - need to setup key values asap rapitive migration (BZ)
    [BZ-11243](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11243)
  - fda calendar exact dates not appearing (BZ)
    [BZ-11259](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11259)
  - debug raptive in article ad not loading 2 (BZ)
    [BZ-11280](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11280)
  - fix fda calendar not rendering server side (BZ)
    [BZ-11283](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11283)
  - exclude analyst ratings with no price target from aggregated calculations (BZ)
    [BZ-11284](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11284)
  - fix missing prop for quote layout gov trades (BZ)
    [BZ-11287](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11287)
  - fix change your password link on account page (BZ)
    [BZ-11294](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11294)
  - check why charts are displaying weird on reports page should be chart for current day (BZ)
    [BZ-11296](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11296)
  - quote page fix (BZ)
    [BZ-11310](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11310)
  - fix after hour and intraday prices on quote page (BZ)
    [BZ-11317](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11317)
  - fix crypto price not displaying in quote header (BZ)
    [BZ-11320](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11320)
  - fix incorrect calendar fallback being used (BZ)
    [BZ-11321](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11321)
  - mobile navigation logo has wrong color and footer ad overlaps mobile menu when open (BZ)
    [BZ-11345](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11345)
  - fix font flashing for global quote page (BZ)
    [BZ-11368](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11368)
  - fix raptive ad not loading on quote page earnings tab (BZ)
    [BZ-11377](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11377)
  - fix connatix preload (BZ)
    [BZ-11383](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11383)
  - fix previous close price not showing up during no trading (BZ)
    [BZ-11387](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11387)
  - fix missing toggle for login register (BZ)
    [BZ-11394](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11394)
  - bz ptype is undefined (BZ)
    [BZ-11399](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11399)
  - debug blank page appearing when switching between tabs on home page (BZ)
    [BZ-11400](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11400)
  - benzigna description info block on quote page missing (BZ)
    [BZ-11403](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11403)
  - fix overlaps for soft paywall (BZ)
    [BZ-11422](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11422)
  - error on some etf quote pages (BZ)
    [BZ-11434](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11434)
  - final debug raptive ads not loading (BZ)
    [BZ-11438](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11438)
  - insert log for global quotes (BZ)
    [BZ-11443](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11443)
  - dynamically inject raptive ad script to fix ads not loading in place of placeholders (BZ)
    [BZ-11445](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11445)
  - fix mobile ads being hidden (BZ)
    [BZ-11446](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11446)
  - fix bull vs bear repeating text (BZ)
    [BZ-11455](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11455)
  - the content on the report pages is entirely blurred (BZ)
    [BZ-11456](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11456)
  - fix analyticsbox not showing up (BZ)
    [BZ-11491](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11491)
  - fix ad not being disabled on staging (BZ)
    [BZ-11494](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11494)
  - fix build error (BZ)
    [BZ-11498](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11498)
  - fix hydration errors on article pages (BZ)
    [BZ-11508](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11508)
  - amp errors do not add script tags using next head see script tag with src https cdn (BZ)
    [BZ-11541](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11541)
  - login fix (BZ)
    [BZ-11543](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11543)
  - typeerror err invalid char invalid character in header content surrogate key (BZ)
    [BZ-11548](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11548)
  - fix webinar url (BZ)
    [BZ-11577](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11577)
  - fix blank homepage after logged out (BZ)
    [BZ-11578](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11578)
  - error attaching third party targeting typeerror cannot read properties of undefined reading (BZ)
    [BZ-11579](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11579)
  - fix cls for banner on mobile (BZ)
    [BZ-11583](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11583)
  - fix usebenzingaedge hook erroring on india app (BZ)
    [BZ-11587](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11587)
  - bug switching tabs on analyst ratings calendar (BZ)
    [BZ-11603](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11603)
  - style issue with quote header due to raptive ad placeholder css (BZ)
    [BZ-11606](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11606)
  - fix duplicate ad unit showing up in some article sidebars (BZ)
    [BZ-11609](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11609)
  - fix api v1 report supported tickers v2 from getting called too many times (BZ)
    [BZ-11615](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11615)
  - price sometimes missing on profile page (BZ)
    [BZ-11622](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11622)
  - fix hydration errors caused by raptive scripts (BZ)
    [BZ-11624](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11624)
  - fix verified email in account page (BZ)
    [BZ-11627](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11627)
  - calendar is blurred when logged in to edge (BZ)
    [BZ-11639](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11639)
  - fix typo in about page (BZ)
    [BZ-11642](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11642)
  - fix duplicate quotedelayed requests being called for desktop mobile navigation header (BZ)
    [BZ-11644](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11644)
  - cors error create wrapper for crypto quote bar calls also hide api key from frontend (BZ)
    [BZ-11646](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11646)
  - fix incorrect hostname being applied (BZ)
    [BZ-11647](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11647)
  - fix broken flag img for earnings calendar (BZ)
    [BZ-11656](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11656)
  - color issue on the stock page graph (BZ)
    [BZ-11661](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11661)
  - trending tab on home page loads blank when you navigate to another tab and then back (BZ)
    [BZ-11675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11675)
  - fix user not be authenticated in comments drawer (BZ)
    [BZ-11693](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11693)
  - benzinga not all share buttons trigger on mobile (BZ)
    [BZ-11722](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11722)
  - fix paywall logic (BZ)
    [BZ-11725](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11725)
  - fix ui issues with auto renew selector on accounts billing page (BZ)
    [BZ-11739](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11739)
  - quote page insider trades are broken (BZ)
    [BZ-11743](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11743)
  - fix banner typo (BZ)
    [BZ-11810](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11810)
  - put taboola back on site (BZ)
    [BZ-11822](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11822)
  - fix research services from being blocked by mobile app banner ad (BZ)
    [BZ-11827](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11827)
  - fix connatix live loop (BZ)
    [BZ-11843](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11843)
  - etf links aren t popping (BZ)
    [BZ-11845](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11845)
  - typeerror n sharepercentage tofixed is not a function (BZ)
    [BZ-11847](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11847)
  - fix incorrect key in raptive ad placement bz clean up (BZ)
    [BZ-11855](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11855)
  - fix ssr on serversidecalendar (BZ)
    [BZ-9921](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9921)

## Task
  - updated sheet api (BZ)
    [BZ-0000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/0000)
  - benzinga optimize bundle size for article page (BZ)
    [BZ-10020](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10020)
  - fix missing table footer source (BZ)
    [BZ-10035](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10035)
  - tell search engines to skip certain content on the page (BZ)
    [BZ-10076](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10076)
  - add analytics to article campaign popup (BZ)
    [BZ-10087](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10087)
  - investigate parsely cookies (BZ)
    [BZ-10088](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10088)
  - migrate alts page to have all alt types (BZ)
    [BZ-10090](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10090)
  - Adds free benzinga pro trial to dropdown menu for tools (BZ)
    [BZ-10091](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10091)
  - reduce update time for breaking news banner if more than 1 item (BZ)
    [BZ-10092](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10092)
  - images should use cdn instead of www on topic category pages (BZ)
    [BZ-10102](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10102)
  - alts news feed in sidebar no infinite load (BZ)
    [BZ-10110](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10110)
  - alts screener sorting does not work (BZ)
    [BZ-10117](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10117)
  - the tweet x button on articles is not working for me it opens the window but then gives me a (BZ)
    [BZ-10120](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10120)
  - ga article link update to ga4 link (BZ)
    [BZ-10122](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10122)
  - remove duplicate block tsx (BZ)
    [BZ-10126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10126)
  - small improvements to quotev2 pages (BZ)
    [BZ-10127](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10127)
  - widget analytics context (BZ)
    [BZ-10128](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10128)
  - change primis video placement on quotev2 page (BZ)
    [BZ-10129](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10129)
  - replace mgid placements on quotes pages with taboola placements (BZ)
    [BZ-10139](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10139)
  - fix ld json page type for money articles (BZ)
    [BZ-10140](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10140)
  - migrate alts to profile configuration (BZ)
    [BZ-10141](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10141)
  - change divs to quotemetrics in table (BZ)
    [BZ-10143](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10143)
  - fix ticker bar cls for desktop on com (BZ)
    [BZ-10144](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10144)
  - ic sidebar items (BZ)
    [BZ-10147](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10147)
  - fully noindex prs (BZ)
    [BZ-10154](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10154)
  - add more functionality to benzinga pro button on com (BZ)
    [BZ-10156](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10156)
  - follow up from resolve fix ad not loading on premarket page (BZ)
    [BZ-10160](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10160)
  - reduce node memory allocation for benzinganext docker (BZ)
    [BZ-10169](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10169)
  - add company offering reviews (BZ)
    [BZ-10170](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10170)
  - email capture workflow for un monetized links (BZ)
    [BZ-10171](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10171)
  - increase taboola sov within waterfall logic (BZ)
    [BZ-10173](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10173)
  - keep state of symbols when switching between charts on new quote page (BZ)
    [BZ-10186](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10186)
  - updated welcome page design (BZ)
    [BZ-10189](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10189)
  - add query param trigger for quote page get report form (BZ)
    [BZ-10192](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10192)
  - migrate offerings page to profile layout (BZ)
    [BZ-10196](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10196)
  - etf cards (BZ)
    [BZ-10214](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10214)
  - template cleanup (BZ)
    [BZ-10226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10226)
  - add a nav bar link to benzinga sports insights (BZ)
    [BZ-10229](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10229)
  - profile notification banner text link (BZ)
    [BZ-10230](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10230)
  - canonical url on calendar page has a trailing slash (BZ)
    [BZ-10234](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10234)
  - re index prodigy press (BZ)
    [BZ-10235](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10235)
  - noindex some translated content others add the proper redirect or canonical (BZ)
    [BZ-10236](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10236)
  - fix table height and latest news header (BZ)
    [BZ-10237](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10237)
  - noindex duplicate content feeds under content (BZ)
    [BZ-10241](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10241)
  - update report supporttickers tickers endpoint to use internal route (BZ)
    [BZ-10242](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10242)
  - manager should have a local cache for report supportedtickers (BZ)
    [BZ-10243](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10243)
  - change document lang from zh cn to zh hans (BZ)
    [BZ-10244](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10244)
  - more templatizing work (BZ)
    [BZ-10247](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10247)
  - more templatizing work 2 (BZ)
    [BZ-10247](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10247)
  - fix india article sidebar loading (BZ)
    [BZ-10248](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10248)
  - remove nofollow from author pages but not on individual articles (BZ)
    [BZ-10253](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10253)
  - increase taboola sov within waterfall logic (BZ)
    [BZ-10257](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10257)
  - resolve desgin issue on offering sidebar (BZ)
    [BZ-10258](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10258)
  - update article clear cache endpoint to lavapress (BZ)
    [BZ-10259](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10259)
  - up welcome workflow to 100 one tap (BZ)
    [BZ-10264](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10264)
  - increase sov for connatix across article pages (BZ)
    [BZ-10267](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10267)
  - update peers chart styling (BZ)
    [BZ-10269](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10269)
  - benzinga improve font re sizing loading for site cls cwv (BZ)
    [BZ-10270](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10270)
  - buttons should have accessible names (BZ)
    [BZ-10271](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10271)
  - benzinga remove duplicate id author name 2 (BZ)
    [BZ-10272](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10272)
  - update main chart styling (BZ)
    [BZ-10274](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10274)
  - update affiliate program footer link (BZ)
    [BZ-10280](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10280)
  - debug taboola triggering before lazy loading (BZ)
    [BZ-10286](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10286)
  - create google ad units on quote pages (BZ)
    [BZ-10293](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10293)
  - uscf fund page changes (BZ)
    [BZ-10300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10300)
  - com mobile header footer optimizations (BZ)
    [BZ-10302](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10302)
  - add 2 nav bar links (BZ)
    [BZ-10313](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10313)
  - handle translation response from layout request (BZ)
    [BZ-10318](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10318)
  - submit review entries (BZ)
    [BZ-10319](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10319)
  - optimize connatix and optimistically include preloads and header scripts (BZ)
    [BZ-10325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10325)
  - benzinga replace shortsqueeze references with benzinga apis (BZ)
    [BZ-10326](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10326)
  - update calendarswidget tabs font style (BZ)
    [BZ-10333](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10333)
  - embeds in articles should be aligned middle rather than aligned left (BZ)
    [BZ-10337](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10337)
  - benzinga identify and fix pages returning withour proper data (BZ)
    [BZ-10339](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10339)
  - add log to help debug ui displaying typeerror when trying to send out an editorial push (BZ)
    [BZ-10340](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10340)
  - move quotev2 calculation logic to be done server side (BZ)
    [BZ-10341](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10341)
  - change ad and news layout on quotev2 page (BZ)
    [BZ-10342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10342)
  - update noindex rules on quotev2 (BZ)
    [BZ-10343](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10343)
  - benzinga add schema articlebody to article pages (BZ)
    [BZ-10347](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10347)
  - prevent empty go links requests for money content server (BZ)
    [BZ-10349](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10349)
  - return 410 for comtext content types (BZ)
    [BZ-10353](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10353)
  - benzinga add 2 top stories to the homepage on mobile (BZ)
    [BZ-10356](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10356)
  - resolve about us page changes (BZ)
    [BZ-10363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10363)
  - benzinga ads should be collapsible by default if unit is below the fold lazy loaded (BZ)
    [BZ-10364](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10364)
  - benzinga improve font re sizing loading for site cls cwv part 2 1 (BZ)
    [BZ-10367](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10367)
  - all reports pages (BZ)
    [BZ-10370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10370)
  - create a page to list all available reports (BZ)
    [BZ-10370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10370)
  - update can t miss episodes on the razreport page (BZ)
    [BZ-10378](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10378)
  - few fields cut off in the ratings calendar (BZ)
    [BZ-10380](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10380)
  - update connatix video player props on quotev2 page (BZ)
    [BZ-10382](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10382)
  - introduce speakable into structured data (BZ)
    [BZ-10398](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10398)
  - finishing touches to quotev2 page (BZ)
    [BZ-10399](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10399)
  - update header images on razreport page (BZ)
    [BZ-10400](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10400)
  - remove from comtexpublishers array (BZ)
    [BZ-10407](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10407)
  - benzinga set fixed height for connatix video player on article desktop (BZ)
    [BZ-10411](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10411)
  - disable connatix video unit (BZ)
    [BZ-10413](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10413)
  - timestamp not lining up in recent page (BZ)
    [BZ-10414](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10414)
  - probe image size endpoint (BZ)
    [BZ-10415](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10415)
  - update main title on razreport page (BZ)
    [BZ-10419](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10419)
  - direct d tickers to quotev2 using middleware (BZ)
    [BZ-10422](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10422)
  - prevent additional api redirect checks (BZ)
    [BZ-10423](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10423)
  - increase taboola sov all time high in vrpm lowest page views in 6 months (BZ)
    [BZ-10424](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10424)
  - benzinga re enable and remove content types pr prlog 1476158 globe pr globeprwire (BZ)
    [BZ-10431](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10431)
  - set connatix video player live again across article pages quote pages and home page (BZ)
    [BZ-10438](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10438)
  - set connatix video player live again across article pages quote pages and home page 2 (BZ)
    [BZ-10438](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10438)
  - replace analyst ratings component on quotev2 analyst ratings with newer component (BZ)
    [BZ-10451](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10451)
  - replicate noindexing rules on quote pages to match quotev2 (BZ)
    [BZ-10452](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10452)
  - author page content type (BZ)
    [BZ-10457](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10457)
  - update welcome flow on com (BZ)
    [BZ-10471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10471)
  - update benzinga com premarket page (BZ)
    [BZ-10472](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10472)
  - change pro button in com top bar to point to https www benzinga com pro register utm source (BZ)
    [BZ-10475](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10475)
  - improve quote v2 button color for top ctas (BZ)
    [BZ-10485](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10485)
  - fetch list of available reports once an hour (BZ)
    [BZ-10486](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10486)
  - update welcome flow if user skips set isuseronboarded as true (BZ)
    [BZ-10487](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10487)
  - increase taboola offset (BZ)
    [BZ-10493](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10493)
  - re enable softpaywall with changes for targeting tags (BZ)
    [BZ-10498](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10498)
  - benzinga fix cls shift in quote bar (BZ)
    [BZ-10512](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10512)
  - do not redirect topic channel pages from global sites (BZ)
    [BZ-10515](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10515)
  - include nav bar link to living under money consumer (BZ)
    [BZ-10519](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10519)
  - add a check for date confirmed before rendering earnings info on quote pages (BZ)
    [BZ-10523](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10523)
  - etfs integrate new frontend designs (BZ)
    [BZ-10531](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10531)
  - redesign etf quotes pages (BZ)
    [BZ-10532](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10532)
  - disallow secfilings in the robots txt (BZ)
    [BZ-10535](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10535)
  - connatix player code update for old and new quote pages pre market page (BZ)
    [BZ-10537](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10537)
  - update link in podcastslistblock (BZ)
    [BZ-10539](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10539)
  - add title to quotev2 overview tab (BZ)
    [BZ-10541](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10541)
  - spin up redis to work with local nextjs dev environment (BZ)
    [BZ-10548](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10548)
  - benzinga release all quote pages (BZ)
    [BZ-10552](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10552)
  - benzinga quotes v2 should handle cyrpto (BZ)
    [BZ-10553](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10553)
  - use one of the peers to compare to current ticker initially (BZ)
    [BZ-10554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10554)
  - rework taboola offset let s discuss options (BZ)
    [BZ-10556](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10556)
  - migrate stockinfomessages to quotev2 header (BZ)
    [BZ-10561](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10561)
  - update urls on podcast pages (BZ)
    [BZ-10564](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10564)
  - change meta twitter card value to summary large image on article pages (BZ)
    [BZ-10573](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10573)
  - investigate cached user agent (BZ)
    [BZ-10584](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10584)
  - benzinga improve font re sizing loading for site cls cwv part 3 (BZ)
    [BZ-10586](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10586)
  - add guidance tab to new quote pages (BZ)
    [BZ-10602](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10602)
  - default to account creation for login page (BZ)
    [BZ-10603](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10603)
  - default color and font updates to podcastcard (BZ)
    [BZ-10617](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10617)
  - improvements insider trades on quote pages (BZ)
    [BZ-10624](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10624)
  - quotes page add tooltip for technicals financials analysis (BZ)
    [BZ-10627](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10627)
  - quotes page feedback on graphs numbers etc (BZ)
    [BZ-10628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10628)
  - quotes page underline connecting key stats to data (BZ)
    [BZ-10629](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10629)
  - update ticket hover to include sponsorship from public (BZ)
    [BZ-10631](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10631)
  - clicking my stocks cta should launch account creation not another landing page (BZ)
    [BZ-10638](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10638)
  - switch back to login as default (BZ)
    [BZ-10642](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10642)
  - noindex quotes news pages when 5 news exists for a company (BZ)
    [BZ-10645](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10645)
  - missing canonicals on report reports pages (BZ)
    [BZ-10650](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10650)
  - small updates post new podcast page (BZ)
    [BZ-10651](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10651)
  - add company name to report page tickers (BZ)
    [BZ-10661](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10661)
  - correct font paths in bzheader duplicate paths (BZ)
    [BZ-10670](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10670)
  - make importing aggrid stylesheets dynamic (BZ)
    [BZ-10671](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10671)
  - optimize podcast pages (BZ)
    [BZ-10672](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10672)
  - hide peers section in sidebar if there are no peers (BZ)
    [BZ-10675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10675)
  - revive fix any errors with the existing account page (BZ)
    [BZ-10678](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10678)
  - address quotev2 getting indexed 2 (BZ)
    [BZ-10683](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10683)
  - changes to quote analyst ratings page (BZ)
    [BZ-10690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10690)
  - mobile responsiveness updates to podcasts showslug episodeslug page (BZ)
    [BZ-10693](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10693)
  - exclude japanase korean from author page (BZ)
    [BZ-10694](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10694)
  - update asset ratings on quote page more favorable for partners (BZ)
    [BZ-10703](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10703)
  - update translate buttons on article pages to newcms links (BZ)
    [BZ-10705](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10705)
  - set up logos database page (BZ)
    [BZ-10708](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10708)
  - remove the guides link from the navigation bar (BZ)
    [BZ-10717](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10717)
  - remove yolowire from comtex publishers list (BZ)
    [BZ-10726](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10726)
  - add height property to advertisingcardblock (BZ)
    [BZ-10741](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10741)
  - disallow or noindex authors in robots txt (BZ)
    [BZ-10742](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10742)
  - gov trades show both transaction and report date (BZ)
    [BZ-10748](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10748)
  - remove conditional noindex tag on benzinga neuro currently noindexed after 30 days (BZ)
    [BZ-10749](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10749)
  - update insider report links on benzinga com website (BZ)
    [BZ-10760](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10760)
  - add branding to cta value prop article unit (BZ)
    [BZ-10772](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10772)
  - gov trades table description for each tab (BZ)
    [BZ-10774](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10774)
  - quote connatix live (BZ)
    [BZ-10791](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10791)
  - gov trades include search bar in drilldownpages (BZ)
    [BZ-10794](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10794)
  - update analyst ratings widgets on quote page analyst ratings tab (BZ)
    [BZ-10796](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10796)
  - campaignify ads should not load if referrer is finviz add to list with robinhood (BZ)
    [BZ-10801](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10801)
  - move account cta under comments button (BZ)
    [BZ-10803](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10803)
  - revert to postmessage redirect on login if param set (BZ)
    [BZ-10808](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10808)
  - re organize com money menu items (BZ)
    [BZ-10820](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10820)
  - equal heights cards on alternative screener (BZ)
    [BZ-10824](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10824)
  - benzinga blocker referrers robinhood finviz etc should disable investingchannel dynamic units (BZ)
    [BZ-10840](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10840)
  - memory leak investigation (BZ)
    [BZ-10851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10851)
  - benzinga move below article comments below copyright (BZ)
    [BZ-10854](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10854)
  - benzinga add analytyics events and login tracking for comments (BZ)
    [BZ-10855](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10855)
  - update campaignify on analyst ratings calendar (BZ)
    [BZ-10856](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10856)
  - display uncolored segments in analyst rating bar when no data exists (BZ)
    [BZ-10863](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10863)
  - exclude global content types from author page (BZ)
    [BZ-10877](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10877)
  - disable top overview rating header when data is not available (BZ)
    [BZ-10879](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10879)
  - figure out why premarket data does not display open close correctly reinstitute conditional (BZ)
    [BZ-10881](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10881)
  - update to services page (BZ)
    [BZ-10883](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10883)
  - market news and data brought to you by benzinga apis (BZ)
    [BZ-10885](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10885)
  - update pro streamline banner (BZ)
    [BZ-10889](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10889)
  - noindex https www benzinga com trade ideas trader 1969 (BZ)
    [BZ-10893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10893)
  - only login user into coral comments after they have opened the comments drawer (BZ)
    [BZ-10895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10895)
  - update analyst widgets on ratings quote tab (BZ)
    [BZ-10903](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10903)
  - remove deutsch menu section s (BZ)
    [BZ-10905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10905)
  - update incorrectly named properties in checkdevicetype method (BZ)
    [BZ-10910](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10910)
  - fix yield report price (BZ)
    [BZ-10915](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10915)
  - benzinga move news see more above taboola unit (BZ)
    [BZ-10918](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10918)
  - remove quote widget from single etf page (BZ)
    [BZ-10924](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10924)
  - remove sports from the menu (BZ)
    [BZ-10925](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10925)
  - remove benzinga pro streamline banner (BZ)
    [BZ-10926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10926)
  - forgot password link inside iframe should target  blank so the link opens in a new tab (BZ)
    [BZ-10936](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10936)
  - remove console log from newslist (BZ)
    [BZ-10940](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10940)
  - login register page should not poll session endpoint (BZ)
    [BZ-10951](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10951)
  - follow up from remove benzinga pro streamline banner (BZ)
    [BZ-10958](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10958)
  - disallow pressreleases in the robots txt (BZ)
    [BZ-10963](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10963)
  - bz special offer title link change (BZ)
    [BZ-10979](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10979)
  - update next to 14 2 2 (BZ)
    [BZ-10982](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10982)
  - update timeframe for earth day logo (BZ)
    [BZ-10987](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10987)
  - statbox title color is getting incorrectly overridden (BZ)
    [BZ-10991](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10991)
  - resolve target project check for appenvironment (BZ)
    [BZ-10992](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10992)
  - hide etf details on etf quote pages (BZ)
    [BZ-11002](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11002)
  - pull out options from the markets section (BZ)
    [BZ-11004](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11004)
  - benzinga setup fusion rum monitoring via coralogix (BZ)
    [BZ-11010](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11010)
  - decrease javascript size on page with light youtube embed (BZ)
    [BZ-11016](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11016)
  - need to add customparam1 section to connatix player code (BZ)
    [BZ-11032](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11032)
  - add underline to table rows in cryptoquotemetrics (BZ)
    [BZ-11036](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11036)
  - invalid next config js options (BZ)
    [BZ-11037](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11037)
  - remove non evergreen logos (BZ)
    [BZ-11046](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11046)
  - resolve youtube block design spacing issue (BZ)
    [BZ-11053](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11053)
  - remove holidays that are declared but not being used (BZ)
    [BZ-11054](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11054)
  - increase coverage of youtubeblock on article pages (BZ)
    [BZ-11055](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11055)
  - lazy load coral comments script (BZ)
    [BZ-11060](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11060)
  - lazy load more ad components (BZ)
    [BZ-11065](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11065)
  - remove other adblocks from quote page expect overview (BZ)
    [BZ-11070](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11070)
  - dividend body copy template (BZ)
    [BZ-11071](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11071)
  - remove custom server in bz (BZ)
    [BZ-11077](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11077)
  - disable campaignify unit based on layout request (BZ)
    [BZ-11080](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11080)
  - update quote page etf sidebar and overview top section (BZ)
    [BZ-11087](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11087)
  - etfs can we use a tooltip to display full description on hover (BZ)
    [BZ-11094](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11094)
  - need confirmation modal on removing card (BZ)
    [BZ-11097](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11097)
  - benzinga disable ugc commenting (BZ)
    [BZ-11112](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11112)
  - bundles optimisation (BZ)
    [BZ-11113](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11113)
  - hydration issues with calendar pages aftermath (BZ)
    [BZ-11119](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11119)
  - redirect content related to hacking to homepage (BZ)
    [BZ-11120](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11120)
  - remove image cache from home page (BZ)
    [BZ-11127](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11127)
  - benzinga add comment visibility to listing (BZ)
    [BZ-11139](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11139)
  - add ascb to publiccomallowedtickers array (BZ)
    [BZ-11140](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11140)
  - update ignoreerrors field for coralogixrum and remove datadogrumloader call (BZ)
    [BZ-11145](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11145)
  - hold benzinga add back comments hacking content back (BZ)
    [BZ-11152](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11152)
  - add 5th image to memorial day top of website banner (BZ)
    [BZ-11156](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11156)
  - benzinga make the whole public chart clickable in the ticker hover (BZ)
    [BZ-11182](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11182)
  - adjust pro headline viewability with an ellipsis vs full content (BZ)
    [BZ-11183](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11183)
  - update middleware prevent unnecessary requests (BZ)
    [BZ-11184](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11184)
  - remove memorial banner for friday 11 59pm (BZ)
    [BZ-11188](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11188)
  - swap top of site banners with new graphics (BZ)
    [BZ-11190](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11190)
  - please update link for orange premium research tab on bz com (BZ)
    [BZ-11191](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11191)
  - add error checks for nextjs news api (BZ)
    [BZ-11194](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11194)
  - Remove France from global menu (BZ)
    [BZ-11200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11200)
  - update robots txt to disallow query params (BZ)
    [BZ-11201](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11201)
  - re work how bz reach content flows to the site (BZ)
    [BZ-11202](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11202)
  - Build branch for convert tools page from page router to app router (BZ)
    [BZ-11208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11208)
  - convert tools page from page router to app router (BZ)
    [BZ-11208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11208)
  - convert tools page from page router to app router v2 (BZ)
    [BZ-11208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11208)
  - convert tools page from page router to app routertask (BZ)
    [BZ-11208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11208)
  - increase z index of navigation header and update div classnames for raptive placeholders (BZ)
    [BZ-11211](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11211)
  - changes to public chart (BZ)
    [BZ-11216](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11216)
  - update comment count link button in authorcontent (BZ)
    [BZ-11227](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11227)
  - links do not have a discernible name (BZ)
    [BZ-11228](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11228)
  - reactive google ad block (BZ)
    [BZ-11230](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11230)
  - remove skip option from welcome flow (BZ)
    [BZ-11231](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11231)
  - resolve real estate screener news data (BZ)
    [BZ-11233](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11233)
  - ask an expert block update (BZ)
    [BZ-11234](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11234)
  - update title color for featured articles on home page (BZ)
    [BZ-11237](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11237)
  - update border bottom color in carousel and news list for consistency (BZ)
    [BZ-11238](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11238)
  - change top of site banner rotation speed to 60 seconds (BZ)
    [BZ-11246](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11246)
  - disable google ad for raptive (BZ)
    [BZ-11249](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11249)
  - remove alphastream from bz (BZ)
    [BZ-11251](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11251)
  - benzinga add mining to the mnu under parent markets (BZ)
    [BZ-11252](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11252)
  - add new cta to public ticker hover unit (BZ)
    [BZ-11253](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11253)
  - disable rotating banner (BZ)
    [BZ-11255](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11255)
  - homepage bundle optimisation (BZ)
    [BZ-11257](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11257)
  - script evaluation optimisation 3 (BZ)
    [BZ-11258](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11258)
  - wind down amp articles hide amp meta tag (BZ)
    [BZ-11260](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11260)
  - real estate unmonetized link modal exit intent modal stopped loading (BZ)
    [BZ-11261](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11261)
  - quick win shortcut etf content on page elevate content (BZ)
    [BZ-11266](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11266)
  - add tab on stock page for government trades (BZ)
    [BZ-11268](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11268)
  - stock forecast added to quote pages (BZ)
    [BZ-11269](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11269)
  - calendar ticker link clicks don t work (BZ)
    [BZ-11270](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11270)
  - add articlepage toprail to article pages at the top of the right rail (BZ)
    [BZ-11285](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11285)
  - apply translations styles to quote page (BZ)
    [BZ-11286](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11286)
  - capture onboarding event history back without type (BZ)
    [BZ-11289](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11289)
  - decrease sessionsamplerate for coralogix rum (BZ)
    [BZ-11295](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11295)
  - fix about page help card submission (BZ)
    [BZ-11297](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11297)
  - configure raptivetarget object (BZ)
    [BZ-11305](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11305)
  - fix keyword search on com (BZ)
    [BZ-11308](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11308)
  - fix keyword search on com v2 (BZ)
    [BZ-11308](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11308)
  - create acf raptive ad placeholder block (BZ)
    [BZ-11309](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11309)
  - enable preload for connatix player (BZ)
    [BZ-11311](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11311)
  - july 4 promos (BZ)
    [BZ-11312](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11312)
  - gov trades ticker search by symbol (BZ)
    [BZ-11313](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11313)
  - update layout endpoint to internal (BZ)
    [BZ-11314](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11314)
  - disable marketfy chat on premarket page (BZ)
    [BZ-11319](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11319)
  - revert update layout endpoint (BZ)
    [BZ-11323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11323)
  - headervariants (BZ)
    [BZ-11324](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11324)
  - change country selector (BZ)
    [BZ-11327](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11327)
  - remove article building console logs (BZ)
    [BZ-11333](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11333)
  - lead magnet copy on account creation (BZ)
    [BZ-11343](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11343)
  - update services to include benzinga premium (BZ)
    [BZ-11351](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11351)
  - add stock of the day to ideas drop down (BZ)
    [BZ-11355](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11355)
  - remove covey from ideas menu (BZ)
    [BZ-11358](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11358)
  - change quote ticker dividend to quote ticker dividends (BZ)
    [BZ-11361](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11361)
  - replace googleadunit with raptiveadplaceholder on quote pages (BZ)
    [BZ-11363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11363)
  - init japan global quote page (BZ)
    [BZ-11369](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11369)
  - create sign up login watchlists for global template (BZ)
    [BZ-11370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11370)
  - create sign up login watchlists for global template 2 (BZ)
    [BZ-11370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11370)
  - real estate hub page (BZ)
    [BZ-11372](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11372)
  - design ad light pages for bz edge subs (BZ)
    [BZ-11374](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11374)
  - open all section of quote global page (BZ)
    [BZ-11376](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11376)
  - add regular price during open market to mainpricebox (BZ)
    [BZ-11379](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11379)
  - resolve money vertical landing page layout (BZ)
    [BZ-11395](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11395)
  - add information tooltip for fund score on etf quote page (BZ)
    [BZ-11401](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11401)
  - update time labels in quoteheaderpricebox (BZ)
    [BZ-11411](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11411)
  - create quicklinks block (BZ)
    [BZ-11416](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11416)
  - update placeholder for raptive ads reenable lazy loading on home page (BZ)
    [BZ-11417](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11417)
  - gov trades add security types to table (BZ)
    [BZ-11421](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11421)
  - add description to gallerycarousel (BZ)
    [BZ-11423](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11423)
  - resolve articlepopup exist intent behaviour (BZ)
    [BZ-11426](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11426)
  - text changes on quote analyst ratings (BZ)
    [BZ-11427](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11427)
  - seo improvements for short interest pages (BZ)
    [BZ-11428](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11428)
  - wing enable indexing 2 author (BZ)
    [BZ-11429](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11429)
  - update etf page to use new api fields (BZ)
    [BZ-11432](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11432)
  - improve seo for insider trades pages cleanup some display issues (BZ)
    [BZ-11433](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11433)
  - update text alignment in etfquotesidebar (BZ)
    [BZ-11436](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11436)
  - re arrange data table on etf pages (BZ)
    [BZ-11441](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11441)
  - fix sponsored content from showing on cannabis page popular stories section (BZ)
    [BZ-11442](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11442)
  - set showcommentsicon to true for all newslist in the bz app (BZ)
    [BZ-11453](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11453)
  - implement picture tag over connatix player while it loads (BZ)
    [BZ-11459](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11459)
  - whitelist chainwire from softpaywall use author (BZ)
    [BZ-11462](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11462)
  - fix bear say bull say spacing on analyst ratings (BZ)
    [BZ-11463](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11463)
  - take down july 4 promo (BZ)
    [BZ-11476](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11476)
  - remove description prop from articleslist in search (BZ)
    [BZ-11479](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11479)
  - add new iterations for paywall (BZ)
    [BZ-11480](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11480)
  - test api benzinga premium test (BZ)
    [BZ-11493](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11493)
  - gate bull vs bear on quote and analyst ratings pages (BZ)
    [BZ-11500](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11500)
  - remove debug logs (BZ)
    [BZ-11501](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11501)
  - clean up date ranges for calendars so no calendars are displayed as blank (BZ)
    [BZ-11511](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11511)
  - more raptive units (BZ)
    [BZ-11524](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11524)
  - update benzinga services page with turning point trader and moneyline link (BZ)
    [BZ-11528](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11528)
  - update coralogix error ignore list (BZ)
    [BZ-11529](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11529)
  - reenable initing in raptive ad manager for benzinga edge (BZ)
    [BZ-11530](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11530)
  - top of website banner is live (BZ)
    [BZ-11531](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11531)
  - fix stock of the day link (BZ)
    [BZ-11534](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11534)
  - add additional raptive unit to longer articles (BZ)
    [BZ-11546](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11546)
  - improve next error logging for non 404 pages (BZ)
    [BZ-11547](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11547)
  - real estate add comments to offerings (BZ)
    [BZ-11551](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11551)
  - handle home page failures (BZ)
    [BZ-11555](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11555)
  - update styles for ads on quote pages (BZ)
    [BZ-11560](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11560)
  - tags for zack s covered tickers (BZ)
    [BZ-11562](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11562)
  - decrease time when calling requestadload (BZ)
    [BZ-11564](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11564)
  - add login register to top bar (BZ)
    [BZ-11565](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11565)
  - create a benzinga edge react context (BZ)
    [BZ-11566](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11566)
  - move welcome flow into auth container (BZ)
    [BZ-11568](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11568)
  - remove pro webinar from top banner rotation (BZ)
    [BZ-11572](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11572)
  - on quotes pages add logo brought to you by benzinga data (BZ)
    [BZ-11574](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11574)
  - find a way to show the ticker hover to some users and not to others (BZ)
    [BZ-11575](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11575)
  - adjust banner size edge case (BZ)
    [BZ-11585](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11585)
  - add additional strings regexes to coralogix ignoreerrors and ignoreurls array (BZ)
    [BZ-11586](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11586)
  - add top raptive ad back in article page sidebar for edge users (BZ)
    [BZ-11604](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11604)
  - add prop trading to menu under money (BZ)
    [BZ-11626](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11626)
  - update contributor link to new contributor portal (BZ)
    [BZ-11634](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11634)
  - update contributor link to new contributor portal about contact page (BZ)
    [BZ-11635](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11635)
  - clean up more coralogix logs (BZ)
    [BZ-11653](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11653)
  - block unusual options activity for edge (BZ)
    [BZ-11655](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11655)
  - a b test hard paywall designs (BZ)
    [BZ-11659](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11659)
  - make entire tweet clickable (BZ)
    [BZ-11666](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11666)
  - update gov trades login wall (BZ)
    [BZ-11667](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11667)
  - updates to new options tab options page (BZ)
    [BZ-11669](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11669)
  - allow exiting soft hard (BZ)
    [BZ-11670](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11670)
  - clean up quote page fundamental charts (BZ)
    [BZ-11673](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11673)
  - add whisper index to ideas dropdown (BZ)
    [BZ-11680](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11680)
  - fix price target section of faq page (BZ)
    [BZ-11683](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11683)
  - clean up current stock price question (BZ)
    [BZ-11684](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11684)
  - optimise translations (BZ)
    [BZ-11709](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11709)
  - prevent paywall on market moving content (BZ)
    [BZ-11729](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11729)
  - perks button on quote pages should be no follow (BZ)
    [BZ-11738](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11738)
  - remove flush from live taboola code on infinite scroll articles (BZ)
    [BZ-11740](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11740)
  - homepage calendar should be gated (BZ)
    [BZ-11753](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11753)
  - keep sticky footer off of all pages for edge users (BZ)
    [BZ-11754](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11754)
  - change desktop ad unit type on premarket page (BZ)
    [BZ-11769](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11769)
  - make ad insertion dynamic for recent news (BZ)
    [BZ-11770](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11770)
  - include options moneyline top of website banner in rotation please (BZ)
    [BZ-11777](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11777)
  - change mobile ad unit size (BZ)
    [BZ-11790](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11790)
  - change chart defaults on quote pages (BZ)
    [BZ-11793](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11793)
  - fix something went wrong text on infinite scroll (BZ)
    [BZ-11809](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11809)
  - remove duplicate raptive ad placements in recent news feed (BZ)
    [BZ-11812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11812)
  - remove page view events from firing within infinite scroll (BZ)
    [BZ-11814](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11814)
  - no s in the ticker bar (BZ)
    [BZ-11820](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11820)
  - match video player height with image height on articles (BZ)
    [BZ-11821](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11821)
  - update top of site banner (BZ)
    [BZ-11823](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11823)
  - move date of action signifier to be after announced (BZ)
    [BZ-11830](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11830)
  - please update moneyline link on services page (BZ)
    [BZ-11836](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11836)
  - add stock brokers under money nav (BZ)
    [BZ-11840](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11840)
  - more tweaks and updates for the gating of content for edge (BZ)
    [BZ-11844](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11844)
  - fix edit new cms link to handle editorial staff vs contributor links (BZ)
    [BZ-11854](https://gitlab.benzinga.io/benzinga/fusion/-/issues/11854)
  - remove sentry from next apps (BZ)
    [BZ-5656](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5656)
  - set surrogate key head for article and listing pages next 2 (BZ)
    [BZ-6422](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6422)
  - logout button s site wide should redirect user to logout 2 (BZ)
    [BZ-8849](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8849)
  - create government trades pages (BZ)
    [BZ-8948](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8948)
  - investigate react tweet (BZ)
    [BZ-9185](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9185)
  - reach quote page touchups (BZ)
    [BZ-9395](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9395)
  - premarket prep page dynamic youtube livestream video (BZ)
    [BZ-9552](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9552)
  - com nft carousel is missing content and the calendar table fields clip content but don t allow (BZ)
    [BZ-9801](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9801)
  - replace calendar campaignify code with ui ad package (BZ)
    [BZ-9830](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9830)
  - integrate sponsored content limits in infinitearticles (BZ)
    [BZ-9843](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9843)
  - integrate sponsored content limits in topic page (BZ)
    [BZ-9844](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9844)
  - integrate sponsored content limits in home page (BZ)
    [BZ-9845](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9845)
  - integrate sponsored content limits in home page 2 (BZ)
    [BZ-9845](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9845)
  - integrate sponsored content limits in quote page (BZ)
    [BZ-9846](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9846)
  - add news section to etfs page (BZ)
    [BZ-9862](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9862)
  - optimize preload hints (BZ)
    [BZ-9936](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9936)
  - migrate benzinga podcasts page to fusion (BZ)
    [BZ-9972](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9972)
  - split homepage tabs into separate pages index trending latest briefs exclusives (BZ)
    [BZ-9992](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9992)

# v2.22.0

## Feature
  - improve core ui table add more functionality (BZ)
    [BZ-7914](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7914)
  - make sectionblockheader more customizable (BZ)
    [BZ-9906](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9906)

## Bug
  - fix my watchlist news news not appearing when the user has watchlist symbols (BZ)
    [BZ-10003](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10003)
  - unnecessarily large image being requested on razreport page (BZ)
    [BZ-10004](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10004)
  - issue with serving campaignify middle and bottom ads (BZ)
    [BZ-10026](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10026)
  - fix gtm load (BZ)
    [BZ-10047](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10047)
  - fix loading ui appearing on quote page ideas tab (BZ)
    [BZ-10048](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10048)
  - fix loading screen popping up on premarket page (BZ)
    [BZ-10050](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10050)
  - follow up from resolve fixed placement for breaking news banner fix for cls 2 (BZ)
    [BZ-10059](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10059)
  - type null is not assignable to type string undefined for robots in libs ui money src utils (BZ)
    [BZ-10061](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10061)
  - fix google one tap not appearing on com (BZ)
    [BZ-10068](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10068)
  - fix logic for script loading (BZ)
    [BZ-10073](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10073)
  - ticker hover overlay issue (BZ)
    [BZ-9914](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9914)
  - remove lazy loading from contact page (BZ)
    [BZ-9973](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9973)
  - livestream error typeerror cannot read properties of undefined reading data (BZ)
    [BZ-9979](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9979)
  - fix incorrect logo in navigation header (BZ)
    [BZ-9981](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9981)
  - correct sorting of news items in contentfeed in the newstemplate (BZ)
    [BZ-9988](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9988)
  - styling issue with podcasts section in the home page sidebar (BZ)
    [BZ-9990](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9990)

## Task
  - update ui of logout page to make it less janky (BZ)
    [BZ-10000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10000)
  - move creditcard and updateavatar out of benzinga core ui (BZ)
    [BZ-10001](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10001)
  - update platform detail card layout (BZ)
    [BZ-10005](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10005)
  - noindex key statistics and ideas page (BZ)
    [BZ-10011](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10011)
  - random ui ux improvements for reits landing page (BZ)
    [BZ-10024](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10024)
  - fix find on button cls issue (BZ)
    [BZ-10027](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10027)
  - update news query for topic pages (BZ)
    [BZ-10029](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10029)
  - include dataskrive pr in search (BZ)
    [BZ-10030](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10030)
  - integrate google adsense tag across all pages (BZ)
    [BZ-10032](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10032)
  - offering layout cleanup (BZ)
    [BZ-10043](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10043)
  - offerings email collection iterations (BZ)
    [BZ-10049](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10049)
  - noindex pr dataskrive after 30 days (BZ)
    [BZ-10057](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10057)
  - robots txt to disallow benzinga com web stories (BZ)
    [BZ-10064](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10064)
  - disable google adsense tag (BZ)
    [BZ-10065](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10065)
  - campaignify deployment via leadgen cms (BZ)
    [BZ-10070](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10070)
  - missing canonical on about (BZ)
    [BZ-10072](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10072)
  - update skeleton in placeholderblock (BZ)
    [BZ-10080](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10080)
  - update colors for stock info messages (BZ)
    [BZ-9034](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9034)
  - ad slots to be alloted to investing channel (BZ)
    [BZ-9668](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9668)
  - add exclusives panel prominently on the homepage (BZ)
    [BZ-9670](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9670)
  - 301 redirect all quote nasdaq pages to traditional pages canonicals on nasdaq x pages (BZ)
    [BZ-9693](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9693)
  - prioritize connatix livestream player in timeframe 2 (BZ)
    [BZ-9890](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9890)
  - premarket page tap targets are not sized appropriately (BZ)
    [BZ-9896](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9896)
  - make modal styles more consistent sitewide (BZ)
    [BZ-9902](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9902)
  - ui ui carousel fix logic for rendering arrows (BZ)
    [BZ-9907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9907)
  - test device headers logic (BZ)
    [BZ-9916](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9916)
  - upgrade ampify (BZ)
    [BZ-9917](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9917)
  - etf page tweaks (BZ)
    [BZ-9925](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9925)
  - about us page refinements (BZ)
    [BZ-9927](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9927)
  - add noindex to benzinga insights content after 30 days (BZ)
    [BZ-9929](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9929)
  - ability to force index topic page (BZ)
    [BZ-9933](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9933)
  - premarket page update (BZ)
    [BZ-9935](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9935)
  - ability to edit news template (BZ)
    [BZ-9939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9939)
  - lead funnel work (BZ)
    [BZ-9940](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9940)
  - go link transaction id (BZ)
    [BZ-9941](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9941)
  - add redirects btc btc usd (BZ)
    [BZ-9943](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9943)
  - advertorial offering card design update (BZ)
    [BZ-9951](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9951)
  - change default table layout for stock movers on premarket page (BZ)
    [BZ-9964](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9964)
  - ui updates to israel page (BZ)
    [BZ-9969](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9969)
  - re enable the microsoft sso (BZ)
    [BZ-9977](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9977)
  - preload the 6 first images in contentfeed in the newstemplate (BZ)
    [BZ-9983](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9983)
  - fixed placement for breaking news banner fix for cls (BZ)
    [BZ-9993](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9993)
  - integrate campaign strategy (BZ)
    [BZ-9995](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9995)
  - add url params to session login register calls (Data.js)
    [DATA-10060](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10060)
  - add time stamp to the news list widget (INDIA)
    [INDIA-9841](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9841)
  - change twitter summary card to large image card (INDIA)
    [INDIA-9860](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9860)
  - headers are from com on multiple pages (INDIA)
    [INDIA-9953](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9953)
  - change the layout for menu bar on the homepage (INDIA)
    [INDIA-9970](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9970)
  - resolve offering card grid (MNY)
    [MNY-10013](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10013)
  - resolve offering card featured layout design issue (MNY)
    [MNY-10015](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10015)
  - stack offering card for advertorial on mobile view (MNY)
    [MNY-10055](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10055)
  - placeholder block replacing the campaign (MNY)
    [MNY-10069](https://gitlab.benzinga.io/benzinga/fusion/-/issues/10069)
  - add vertical custom dimension in ga4 for leadgen events (MNY)
    [MNY-9870](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9870)

# v2.21.0

## Feature
  - add custom cell renderer for aggrid for pro cta (BZ)
    [BZ-9827](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9827)
  - make contentfeed max load amount dynamic (BZ)
    [BZ-9840](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9840)
  - add fetchpriority property to bzimage (BZ)
    [BZ-9926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9926)
  - dividends calendar widget (BZ)
    [BZ-9934](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9934)

## Bug
  - wrapping up any remaining issues with automated report (BZ)
    [BZ-9345](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9345)
  - InvalidArgumentError: fromMillis requires a numerical input but received a undefined with value (BZ)
    [BZ-9833](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9833)
  - story headlines are being cutoff on mobile (BZ)
    [BZ-9852](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9852)
  - fix contentfeed not loading more (BZ)
    [BZ-9864](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9864)
  - benzinga logo is requested twice and file size is larger than it should be (BZ)
    [BZ-9895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9895)
  - logo is being requested for desktop when the device is mobile (BZ)
    [BZ-9920](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9920)

## Task
  - revert global session change (BZ)
    [BZ-9783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9783)
  - etf hubpage changes (BZ)
    [BZ-9795](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9795)
  - halloween logos pro upsell (BZ)
    [BZ-9814](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9814)
  - turn back on alphastream (BZ)
    [BZ-9832](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9832)
  - clean up logic for returning error page (BZ)
    [BZ-9834](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9834)
  - connatix new player (BZ)
    [BZ-9835](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9835)
  - inject div tags in body for optinmonster test (BZ)
    [BZ-9836](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9836)
  - create private debt alt investing screener (BZ)
    [BZ-9837](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9837)
  - accessibility performance improvements (BZ)
    [BZ-9839](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9839)
  - post accessibility performance improvements cleanups (BZ)
    [BZ-9854](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9854)
  - make stock movers component more responsive (BZ)
    [BZ-9856](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9856)
  - disable ad and campaignify units on segments fo pages do not merge (BZ)
    [BZ-9857](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9857)
  - premarket page table improvements (BZ)
    [BZ-9858](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9858)
  - remove covey analyst metrics page (BZ)
    [BZ-9880](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9880)
  - remove slug page error error log (BZ)
    [BZ-9881](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9881)
  - optimize benzinga holiday logo (BZ)
    [BZ-9883](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9883)
  - resolve offering page undefiend warning (BZ)
    [BZ-9887](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9887)
  - fix optinmonster injected html (BZ)
    [BZ-9888](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9888)
  - migrate viewport meta tag from meta tsx to index html (BZ)
    [BZ-9891](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9891)
  - optimize imports performance (BZ)
    [BZ-9892](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9892)
  - hide primis placeholder (BZ)
    [BZ-9893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9893)
  - optimize images on razreport (BZ)
    [BZ-9894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9894)
  - disable optinmonster divs (BZ)
    [BZ-9897](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9897)
  - display upcoming earnings and economic data (BZ)
    [BZ-9899](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9899)
  - tunein iframe loading lazy not working on premarket page (BZ)
    [BZ-9900](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9900)
  - desktop navigation should be disabled when mobile is in use (BZ)
    [BZ-9901](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9901)
  - make core ui table responsive (BZ)
    [BZ-9905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9905)
  - clean up redundant grid related code (BZ)
    [BZ-9909](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9909)
  - change logic of partner disclosure popup (BZ)
    [BZ-9915](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9915)
  - add video to the razreport page (BZ)
    [BZ-9918](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9918)
  - connect bz india menu with the zing cms (INDIA)
    [INDIA-9826](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9826)
  - etf landing page update (MNY)
    [MNY-9871](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9871)

# v2.20.0

## Feature
  - connatix live block (BZ)
    [BZ-9804](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9804)

## Bug
  - search and article page hydration errors (BZ)
    [BZ-9790](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9790)
  - correct podcast title text display (BZ)
    [BZ-9791](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9791)
  - images not being preloaded due to caching issues (BZ)
    [BZ-9797](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9797)
  - unparsable structured data for article (BZ)
    [BZ-9799](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9799)
  - fix incorrect logic for determining user agent (BZ)
    [BZ-9807](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9807)
  - newswire author page not returning more results after initial load (BZ)
    [BZ-9819](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9819)
  - contentfeed new stories button is being shown when there aren t any new stories (BZ)
    [BZ-9822](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9822)

## Task
  - article page cls issues with campaignify (BZ)
    [BZ-9665](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9665)
  - investigate campaignify unit text showing up in google search results description (BZ)
    [BZ-9681](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9681)
  - build article disclosure page linked from advertiser disclosure in sponsored content (BZ)
    [BZ-9782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9782)
  - connatix block (BZ)
    [BZ-9785](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9785)
  - exlcude global site content type on author page (BZ)
    [BZ-9787](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9787)
  - noindex comtex press releases (BZ)
    [BZ-9803](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9803)
  - remove account quote bar (BZ)
    [BZ-9809](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9809)
  - remove some console logs related to the recent user agent changes (BZ)
    [BZ-9815](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9815)
  - recent page performance optimizations (BZ)
    [BZ-9816](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9816)
  - real time updates for israel topic page (BZ)
    [BZ-9820](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9820)
  - lazy load logos in calendar (BZ)
    [BZ-9823](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9823)
  - show livebarindicator when realtime is enabled (BZ)
    [BZ-9824](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9824)
  - change x sharing template to tag benzinga india and figure out other templates with krishnaa (INDIA)
    [INDIA-9780](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9780)
  - mobile carousel not responsive at some viewports (MNY)
    [MNY-9796](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9796)
  - update offering cards layout (MNY)
    [MNY-9810](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9810)

# v2.19.0

## Feature
  - quotes page logo and pricing in search (BZ)
    [BZ-3770](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3770)
  - add pro callout over calendar (BZ)
    [BZ-9772](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9772)
  - connatix block (BZ)
    [BZ-9785](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9785)

## Bug
  - image optimistation performance (BZ)
    [BZ-9683](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9683)
  - benzinga analyst ratings widget company link broken (BZ)
    [BZ-9755](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9755)
  - search page style bugs (BZ)
    [BZ-9786](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9786)
  - remove logs from etf pages (BZ)
    [BZ-9788](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9788)
  - fix menu bar hidden on dot com (BZ)
    [BZ-9798](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9798)

## Task
  - update robots txt to include author sitemap (BZ)
    [BZ-9119](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9119)
  - integrate review screener (BZ)
    [BZ-9177](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9177)
  - wire up etf page (BZ)
    [BZ-9342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9342)
  - secondary bylines in com and pro (BZ)
    [BZ-9417](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9417)
  - benzinga com search missing analytic events (BZ)
    [BZ-9516](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9516)
  - b2c newsletter link updates to four places on website (BZ)
    [BZ-9587](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9587)
  - test page speed increase by self hosting google fonts (BZ)
    [BZ-9608](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9608)
  - optimize images on razreport page (BZ)
    [BZ-9685](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9685)
  - warning for production image optimization with next js the optional sharp package is strongly (BZ)
    [BZ-9686](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9686)
  - console log user agent on article page (BZ)
    [BZ-9689](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9689)
  - fix order of checks in api articles (BZ)
    [BZ-9690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9690)
  - need a bz tag name for zacks s tickers (BZ)
    [BZ-9695](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9695)
  - noindex sec filing pages via next (BZ)
    [BZ-9703](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9703)
  - add back default sidebar (BZ)
    [BZ-9704](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9704)
  - upgrade ampify axios (BZ)
    [BZ-9709](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9709)
  - remove isbot logs (BZ)
    [BZ-9711](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9711)
  - remove 1 year condition for topic pages index (BZ)
    [BZ-9714](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9714)
  - correct taboola sov (BZ)
    [BZ-9718](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9718)
  - etf page polish (BZ)
    [BZ-9720](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9720)
  - make width full for etfs page (BZ)
    [BZ-9722](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9722)
  - remove testimageurl before correct benzingalogo is shown (BZ)
    [BZ-9723](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9723)
  - correct add card bug (BZ)
    [BZ-9724](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9724)
  - update pro button on topbar to be button (BZ)
    [BZ-9725](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9725)
  - correct quote page tab width recalc (BZ)
    [BZ-9727](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9727)
  - fix add button flashing (BZ)
    [BZ-9733](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9733)
  - fix taboola multi load (BZ)
    [BZ-9734](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9734)
  - make page headers bold (BZ)
    [BZ-9735](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9735)
  - remove unused styles (BZ)
    [BZ-9738](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9738)
  - revert ampify (BZ)
    [BZ-9740](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9740)
  - quick etf page patch (BZ)
    [BZ-9743](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9743)
  - resolve amp ga4 (BZ)
    [BZ-9744](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9744)
  - optimize featured card images for mobile (BZ)
    [BZ-9745](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9745)
  - preload manrope fonts (BZ)
    [BZ-9746](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9746)
  - update carousel on razreport page (BZ)
    [BZ-9747](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9747)
  - update sizes prop in featuredsection component (BZ)
    [BZ-9756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9756)
  - add de german in the global menu (BZ)
    [BZ-9758](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9758)
  - increase latest episodes carousel width (BZ)
    [BZ-9759](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9759)
  - premarket page whitespace problem (BZ)
    [BZ-9760](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9760)
  - add featured images preload links in head (BZ)
    [BZ-9764](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9764)
  - premarket page improvements update news feed query increase today s economic data table height (BZ)
    [BZ-9770](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9770)
  - article widgets (BZ)
    [BZ-9771](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9771)
  - preload manrope font for all next apps (BZ)
    [BZ-9774](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9774)
  - test performance with initializing googletaganalytics in a useeffect (BZ)
    [BZ-9775](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9775)
  - lazy load iframes on premarket pages (BZ)
    [BZ-9776](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9776)
  - update image widths in featured section (BZ)
    [BZ-9777](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9777)
  - detect why devicetype evaluates to desktop on mobile (BZ)
    [BZ-9778](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9778)
  - fix istaboolaallowedtoshow (BZ)
    [BZ-9784](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9784)
  - debug topic page noindex issue (BZ)
    [BZ-9793](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9793)
  - migrate from axios to node fetch (BZM)
    [BZM-8411](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8411)
  - make ring the bell sidebar widget pop (INDIA)
    [INDIA-9371](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9371)
  - improve adrticle title and description experience on homepage render web (INDIA)
    [INDIA-9428](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9428)
  - make main menu navbar (MNY)
    [MNY-9475](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9475)
  - money pages template update (MNY)
    [MNY-9754](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9754)
  - get menu by custom location (MNY)
    [MNY-9763](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9763)

# v2.18.0

## Bug
  - remove reduce error minified react error 418 errors (BZ)
    [BZ-9538](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9538)
  - typeerror err unescaped characters request path contains unescaped characters (BZ)
    [BZ-9629](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9629)
  - typeerror cannot read properties of undefined reading map at getpeersdividend (BZ)
    [BZ-9645](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9645)
  - typeerror cannot read properties of undefined reading direction (BZ)
    [BZ-9651](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9651)
  - typeerror cannot read properties of null reading hidecompetitors (BZ)
    [BZ-9654](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9654)
  - error child format error articleid 26588305 typeerror err invalid url invalid url (BZ)
    [BZ-9655](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9655)

## Task
  - seo improvements on quote dividend pages (BZ)
    [BZ-8917](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8917)
  - seo improvements on quote dividend pages v2 (BZ)
    [BZ-8917](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8917)
  - add 9 25 9 28 ccc logo (BZ)
    [BZ-9635](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9635)
  - revert contentheadline change (BZ)
    [BZ-9638](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9638)
  - update api key in calendar service (BZ)
    [BZ-9640](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9640)
  - correct additional nav tab dropdown (BZ)
    [BZ-9641](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9641)
  - add default image width and height for og image (BZ)
    [BZ-9644](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9644)
  - lazy import layoutadmin (BZ)
    [BZ-9658](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9658)
  - decode html in title attribute (BZ)
    [BZ-9659](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9659)
  - change ad scripts to preloaded links (BZ)
    [BZ-9662](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9662)
  - change updated date to created date in newslistitemelement (BZ)
    [BZ-9664](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9664)
  - update market alerts list (BZ)
    [BZ-9671](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9671)
  - hide campaignify units from bots (BZ)
    [BZ-9675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9675)
  - mobile style updates to razreport page (BZ)
    [BZ-9676](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9676)
  - add google inspectiontool user agent to checkdevicetype (BZ)
    [BZ-9680](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9680)
  - update design for gainer loser widget (MNY)
    [MNY-9631](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9631)
  - remove compare products read more details section (MNY)
    [MNY-9661](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9661)
  - only trigger popup on money upon exitintent (MNY)
    [MNY-9674](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9674)

# v2.17.0

## Bug
  - amp should support wistia video player (BZ)
    [BZ-9526](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9526)
  - api failing on production (BZ)
    [BZ-9530](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9530)
  - calendar data set fetch fail typeerror items foreach is not a function (BZ)
    [BZ-9535](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9535)
  - api article analytics fails because of undefined benzinga token (BZ)
    [BZ-9540](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9540)
  - api articles draft fails because of undefined benzinga token (BZ)
    [BZ-9543](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9543)
  - follow up from resolve api articles draft fails because of undefined benzinga token (BZ)
    [BZ-9544](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9544)
  - api articles draft still fails (BZ)
    [BZ-9545](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9545)
  - taboola placement fix (BZ)
    [BZ-9548](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9548)
  - fix homepage tabs for smaller layout (BZ)
    [BZ-9563](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9563)
  - hydration issue on money pages (BZ)
    [BZ-9566](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9566)
  - fix wistia not showing up on amp article page (BZ)
    [BZ-9567](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9567)
  - follow up from resolve fix wistia not showing up on amp article page (BZ)
    [BZ-9570](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9570)
  - fix connatix visibile (BZ)
    [BZ-9580](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9580)
  - futures page erroring (BZ)
    [BZ-9588](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9588)
  - add check for article body in convertwistiaiframetoampwistia function (BZ)
    [BZ-9596](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9596)
  - customer enduser id is missing in the request to get top articles by alphastream (BZ)
    [BZ-9600](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9600)
  - premarket page hydration issues (BZ)
    [BZ-9625](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9625)
  - typeerror  errorresponse2 arraybuffer is not a function (Data.js)
    [DATA-9628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9628)
  - cannot read properties of undefined reading sponsored (INDIA)
    [INDIA-9555](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9555)

## Task
  - memory limit (BZ)
    [BZ-701](https://gitlab.benzinga.io/benzinga/fusion/-/issues/701)
  - add trending topics at the top after the logo (BZ)
    [BZ-9462](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9462)
  - lazyload for infinite articles (BZ)
    [BZ-9502](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9502)
  - correct twitter links (BZ)
    [BZ-9525](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9525)
  - revert dockerfile changes (BZ)
    [BZ-9527](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9527)
  - revert next config js changes in money app (BZ)
    [BZ-9529](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9529)
  - test benzinga token cookie is undefined in production (BZ)
    [BZ-9532](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9532)
  - remove test code in getdraftarticle method (BZ)
    [BZ-9533](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9533)
  - follow up from resolve test benzinga token cookie is undefined in production (BZ)
    [BZ-9534](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9534)
  - fix up some cls and hydration issues (BZ)
    [BZ-9536](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9536)
  - disable some next errors to reduce noise (BZ)
    [BZ-9537](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9537)
  - remove next dynamic twitter block import from blocks (BZ)
    [BZ-9539](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9539)
  - wrapper layoutadmin in nofirstrender (BZ)
    [BZ-9541](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9541)
  - follow up from resolve api article analytics fails because of undefined benzinga token (BZ)
    [BZ-9542](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9542)
  - follow up from resolve api articles draft still fails (BZ)
    [BZ-9546](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9546)
  - remove test code for api articles analytics fails because of undefined benzinga token (BZ)
    [BZ-9547](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9547)
  - test convertwistiaiframetoampwistia function (BZ)
    [BZ-9549](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9549)
  - new campaignify placements on above earnings and analyst calendars (BZ)
    [BZ-9550](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9550)
  - add time param to break cache in article analytics api (BZ)
    [BZ-9551](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9551)
  - api should use request session avoid global (BZ)
    [BZ-9554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9554)
  - add connatix to ads txt (BZ)
    [BZ-9560](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9560)
  - test article draft api not working (BZ)
    [BZ-9561](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9561)
  - clean up previous test code for draft and analytics article apis (BZ)
    [BZ-9568](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9568)
  - adds cache control headers for api draft analytics endpoints (BZ)
    [BZ-9569](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9569)
  - fix gap in between articles in infinite scroll (BZ)
    [BZ-9572](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9572)
  - remove cision premium articles unit (BZ)
    [BZ-9579](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9579)
  - primis direxion video test page (BZ)
    [BZ-9585](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9585)
  - update alt investment lead form design (BZ)
    [BZ-9590](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9590)
  - correct long meta title (BZ)
    [BZ-9601](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9601)
  - test thoughts on optimizations debug poor speeds on bznext (BZ)
    [BZ-9602](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9602)
  - lazyload lower moneyblock components (BZ)
    [BZ-9603](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9603)
  - fix undefined fetch from next (BZ)
    [BZ-9604](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9604)
  - remove usage of alphastream from infinite scroll articles (BZ)
    [BZ-9605](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9605)
  - remove microsoft auth script (BZ)
    [BZ-9606](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9606)
  - rollback next image to bzimage (BZ)
    [BZ-9610](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9610)
  - fix duplicate subscription calls (BZ)
    [BZ-9613](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9613)
  - improve lazy loading in moneyblocks (BZ)
    [BZ-9616](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9616)
  - optimize and remove unused scripts (BZ)
    [BZ-9623](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9623)
  - lazy load primis (BZ)
    [BZ-9624](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9624)
  - un pop latest news on sidebar (INDIA)
    [INDIA-9443](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9443)
  - remove the amply widget ads from india site (INDIA)
    [INDIA-9498](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9498)
  - create a page that lists all the posts in de (MNY)
    [MNY-9068](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9068)
  - money app dockerfile and next config js test (MNY)
    [MNY-9531](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9531)
  - lasso plugin migrate css assets (MNY)
    [MNY-9609](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9609)

# v2.16.0

## Bug
  - article draft not showing up for contributors (BZ)
    [BZ-9524](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9524)
  - warning validatedomnesting figure cannot appear as a descendant of p (BZ)
    [BZ-9487](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9487)
  - connatix amp fix (BZ)
    [BZ-9521](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9521)

## Task
  - ignore console errors from ads securepubads g doubleclick net and istrusted (BZ)
    [BZ-8722](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8722)
  - resolve onesignal errors (BZ)
    [BZ-9364](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9364)
  - dd apm fix cli approach (BZ)
    [BZ-9408](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9408)
  - add nofollow meta for pr types and sponsored types (BZ)
    [BZ-9436](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9436)
  - add path tab selection for account page (BZ)
    [BZ-9447](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9447)
  - fix author page xss issue (BZ)
    [BZ-9490](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9490)
  - fix login styling for otp (BZ)
    [BZ-9494](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9494)
  - exclude translated content type on author page instead of including all content types (BZ)
    [BZ-9518](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9518)

# v2.15.0

## Feature
  - update welcome pages (BZ)
    [BZ-8742](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8742)
  - display big articles in iframe (BZ)
    [BZ-9416](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9416)
  - update welcome pages (BZNext.js)
    [BZNEXT-8742](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8742)

## Bug
  - fix parsing issue (BZ)
    [BZ-9511](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9511)
  - fix every story that matters around the web (BZ)
    [BZ-9515](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9515)

## Task
  - replace twitter icon with new x branding (BZ)
    [BZ-9167](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9167)
  - fix sec filings on content (BZ)
    [BZ-9412](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9412)
  - quote page seo optimize earnings h1 (BZ)
    [BZ-9419](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9419)
  - update benzinga pro button of benzinga com (BZ)
    [BZ-9446](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9446)
  - remove undefined request for logo (BZ)
    [BZ-9508](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9508)
  - add errorboundarys in article page (BZ)
    [BZ-9510](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9510)
  - updates to errorboundary (BZ)
    [BZ-9514](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9514)

# v2.14.0

## Feature
  - onboarding connatix video player august 8 (BZ)
    [BZ-9435](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9435)

## Bug
  - default crypto widget values (BZ)
    [BZ-9130](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9130)
  - 404 old pr sec articles (BZ)
    [BZ-9412](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9412)
  - correct article ad positioning (BZ)
    [BZ-9471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9471)
  - topic pages are not showing the latest news (BZ)
    [BZ-9473](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9473)
  - cannot access property of undefined in campaign tsx (BZ)
    [BZ-9479](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9479)
  - duplicate table in article (BZ)
    [BZ-9480](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9480)
  - benzinga fix error on slug page (BZ)
    [BZ-9483](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9483)
  - hydration issues on article page (BZ)
    [BZ-9486](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9486)
  - taboola return fix (BZ)
    [BZ-9495](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9495)
  - temp remove softpaywall (BZ)
    [BZ-9497](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9497)
  - missing ads in article page sidebar (BZ)
    [BZ-9500](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9500)

## Task
  - sandbox limit req setting (BZ)
    [BZ-690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/690)
  - logout button s site wide should redirect user to logout (BZ)
    [BZ-8849](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8849)
  - follow up from resolve noindex articles under 175 words clean up (BZ)
    [BZ-9257](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9257)
  - replace createsession with global session (BZ)
    [BZ-9327](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9327)
  - debug console logs (BZ)
    [BZ-9340](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9340)
  - lead form on alternative investment page (BZ)
    [BZ-9424](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9424)
  - masterworks update for alt investment listing page (BZ)
    [BZ-9431](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9431)
  - remove unneeded prop (BZ)
    [BZ-9477](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9477)
  - clean up some code types reorganize (BZ)
    [BZ-9481](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9481)
  - update next deployments to use new content server address (BZ)
    [BZ-9488](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9488)
  - update images on razreport page (BZ)
    [BZ-9501](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9501)
  - update environment in content manager (BZ)
    [BZ-9504](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9504)
# v2.13.1

## Bug
  - post 2 13 0 release bugs (BZ)
    [BZ-9456](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9456)
  - fix err http headers sent (BZ)
    [BZ-9460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9460)
  - incorrect types being used in insider trades manager (BZ)
    [BZ-9461](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9461)
  - author s bios are cut in half show more button not working (BZ)
    [BZ-9466](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9466)

# v2.13.0

## Bug
  - fix amp oom (BZ)
    [BZ-8941](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8941)
  - more premarket page bug fixes (BZ)
    [BZ-9442](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9442)
  - post 2 12 2 bug fixes (BZ)
    [BZ-9444](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9444)
  - earnings calendar is displaying a redundant company logo on benzinga com (BZ)
    [BZ-9448](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9448)
  - when clicking options tab on calendarswidget it reverts to the earnings tab (BZ)
    [BZ-9450](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9450)

## Task
  - Disables gitlab config hostpot to fix ci/cd issue (BZ)
    [BZ-681](https://gitlab.benzinga.io/benzinga/fusion/-/issues/681)
  - noindex topic pages that have less than 15 posts in them (BZ)
    [BZ-9066](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9066)
  - add pr newswire11 (BZ)
    [BZ-9453](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9453)
  - test using next router instead of window for moneypagetemplate tabs (BZ)
    [BZ-9454](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9454)
  - home page hydration update (BZ)
    [BZ-9455](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9455)
  - update site title and logo (INDIA)
    [INDIA-9372](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9372)
  - add video ads script (INDIA)
    [INDIA-9373](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9373)
  - remove 1 minute review word from product card (MNY)
    [MNY-9449](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9449)

# v2.12.2

## Feature
  - benzinga com soft paywall after 6 pageviews (BZ)
    [BZ-9256](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9256)
  - benzinga com soft paywall after 6 pageviews (BZ)
    [BZ-9256](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9256)
  - apply scanner support to searchbar (Pro.js)
    [PRO-7467](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7467)
  - add short interest to calendar (Pro.js)
    [PRO-8725](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8725)
  - pro migrate newsdesk notifcaitons to info ticker bar (Pro.js)
    [PRO-9142](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9142)

## Bug
  - tabs flash the overflowing tabs on render and rerender (BZ)
    [BZ-9324](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9324)
  - author profile link accessible from an article written gives a 404 (BZ)
    [BZ-9414](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9414)
  - incorrect word count calculated from article utils (BZ)
    [BZ-9437](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9437)
  - canonical tags not working causing duplicate content (BZ)
    [BZ-9440](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9440)

## Task
  - home page edit side bar edit sidebar hyperlink is appearing on the top right hand side and it (BZ)
    [BZ-8695](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8695)
  - soft paywall after 6 pageviews (BZ)
    [BZ-9256](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9256)
  - taboola sov calculation migration (BZ)
    [BZ-9400](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9400)
  - limit infinite scroll everywhere (BZ)
    [BZ-9403](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9403)
  - update review form ui integration w validation (MNY)
    [MNY-9143](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9143)
  - change font color for money pages (MNY)
    [MNY-9439](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9439)
  - fundamentals ticker details widget (PROTO)
    [PROTO-8157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8157)
  - fundamentals ticker details widget 2 (PROTO)
    [PROTO-8157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8157)

# v2.11.2

## Feature
  - analytics links for articles (BZ)
    [BZ-6116](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6116)
  - benzinga handle india layout (BZ)
    [BZ-9296](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9296)

## Bug
  - tabs missing on dev mode (BZ)
    [BZ-9362](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9362)
  - stock image missing on report symbol pages (BZ)
    [BZ-9382](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9382)
  - editorial push notification query params bug (BZ)
    [BZ-9401](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9401)

## Task
  - new quote page design (BZ)
    [BZ-8950](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8950)
  - migrate premarket prep page to fusion (BZ)
    [BZ-9134](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9134)
  - noindex author authors with less than 15 posts (BZ)
    [BZ-9275](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9275)
  - advertising partner quote components (BZ)
    [BZ-9286](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9286)
  - improve date styling for news items (BZ)
    [BZ-9312](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9312)
  - clean up session creation remove iam package reliance (BZ)
    [BZ-9333](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9333)
  - dd apm finalization (BZ)
    [BZ-9357](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9357)
  - resolve cannot read properties of undefined (BZ)
    [BZ-9363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9363)
  - make description color darker in podcast card on razreport page (BZ)
    [BZ-9370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9370)
  - platform page offering order (BZ)
    [BZ-9378](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9378)
  - load data for quotes block server side improves cls (BZ)
    [BZ-9383](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9383)
  - migrate press release id canonical to fusion (BZ)
    [BZ-9386](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9386)
  - increase taboola sov across article pages (BZ)
    [BZ-9387](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9387)
  - login on staging uses production environment (BZ)
    [BZ-9393](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9393)
  - revert previous changes made to parsearticlebodytoblocks (BZ)
    [BZ-9398](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9398)
  - alternative investment offering count issue (BZ)
    [BZ-9404](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9404)
  - increase node memory in dockerfile for next (BZ)
    [BZ-9405](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9405)
  - new quote page design (BZNext.js)
    [BZNEXT-8950](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8950)

# v2.11.1

## Feature
  - menu workspace bar update 3 (BZ)
    [BZ-8907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8907)
  - fda calendar separate tab for pdufa dates (BZNext.js)
    [BZNEXT-4515](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4515)
  - menu workspace bar update (BZNext.js)
    [BZNEXT-8907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8907)
  - menu workspace bar update 2 (BZNext.js)
    [BZNEXT-8907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8907)
  - menu workspace bar update 3 (BZNext.js)
    [BZNEXT-8907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8907)

## Bug
  - body is missing despite body existing in the feed (BZ)
    [BZ-9151](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9151)
  - sponsored content isnt showing up in infinite scroll (BZ)
    [BZ-9158](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9158)
  - resolve bznext deploy bug (BZ)
    [BZ-9178](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9178)
  - cannabis recent news is redirecting to recent page (BZ)
    [BZ-9181](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9181)
  - fix redirecting to undefined issue (BZ)
    [BZ-9184](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9184)
  - current price and upside downside potential blank on analyst ratings page (BZ)
    [BZ-9254](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9254)
  - fix campaign utm for analyst ratings page (BZ)
    [BZ-9289](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9289)
  - wrong canonical tag on market roundup page (BZ)
    [BZ-9299](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9299)
  - no data for author on author page but it still renders (BZ)
    [BZ-9306](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9306)
  - news slug route is empty (BZ)
    [BZ-9325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9325)
  - gravityform cannot read properties of undefined reading displayname (BZNext.js)
    [BZNEXT-9131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9131)

## Task
  - login page design touchups (BZ)
    [BZ-9058](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9058)
  - noindex automatically generated content after 60 days (BZ)
    [BZ-9072](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9072)
  - ad layout updates (BZ)
    [BZ-9080](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9080)
  - develop razreport page (BZ)
    [BZ-9096](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9096)
  - replace premium link with campaignify and page updates (BZ)
    [BZ-9110](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9110)
  - use the same campaign layout logic for amp non amp pages (BZ)
    [BZ-9114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9114)
  - author page bug translated articles showing (BZ)
    [BZ-9129](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9129)
  - reduce table height for analysts with limited ratings on the analyst ratings analyst page (BZ)
    [BZ-9132](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9132)
  - fix amply rendering (BZ)
    [BZ-9145](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9145)
  - fix header rendering bug (BZ)
    [BZ-9162](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9162)
  - bz special offer tab (BZ)
    [BZ-9163](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9163)
  - resolve bznext deploy issue (BZ)
    [BZ-9164](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9164)
  - update research button link (BZ)
    [BZ-9180](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9180)
  - noindex articles under 175 words that are older than 90 days (BZ)
    [BZ-9192](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9192)
  - remove console log (BZ)
    [BZ-9193](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9193)
  - noindex articles under 175 words clean up (BZ)
    [BZ-9246](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9246)
  - fix siderbar undefined (BZ)
    [BZ-9247](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9247)
  - remove redirect on article pages (BZ)
    [BZ-9248](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9248)
  - update robots to allow ads crawling (BZ)
    [BZ-9255](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9255)
  - lower benzinga next india proto money docker memory threshold (BZ)
    [BZ-9258](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9258)
  - refine startup screener ux (BZ)
    [BZ-9273](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9273)
  - config dd apm (BZ)
    [BZ-9280](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9280)
  - login mobile ux (BZ)
    [BZ-9295](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9295)
  - razreport seo improvements (BZ)
    [BZ-9298](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9298)
  - remove available property types on the collectibles screener (BZ)
    [BZ-9300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9300)
  - show latest commit hash in console (BZ)
    [BZ-9304](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9304)
  - make font smaller on homepage featured stories mobile (BZ)
    [BZ-9309](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9309)
  - fix replace undefined error bznext (BZ)
    [BZ-9314](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9314)
  - fix dd version (BZ)
    [BZ-9338](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9338)
  - swap amply homepage slot for gam (BZ)
    [BZ-9347](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9347)
  - revert font changes on dot com (BZ)
    [BZ-9351](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9351)
  - update campaign urls (BZ)
    [BZ-9354](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9354)
  - change the main nav item from personal finance to money (BZ)
    [BZ-9355](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9355)
  - mobile app add snapshot tests (BZM)
    [BZM-8656](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8656)
  - seo improvements on quote dividend pages (BZNext.js)
    [BZNEXT-8917](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8917)
  - market news alerts editorial trigger integration (BZNext.js)
    [BZNEXT-9002](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9002)
  - login page design touchups (BZNext.js)
    [BZNEXT-9058](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9058)
  - missing canonical tag on login page (BZNext.js)
    [BZNEXT-9073](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9073)
  - show prs from same author on the article press release page (BZNext.js)
    [BZNEXT-9086](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9086)
  - replace premium link with campaignify and page updates (BZNext.js)
    [BZNEXT-9110](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9110)
  - add more register types (BZNext.js)
    [BZNEXT-9112](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9112)
  - use the same campaign layout logic for amp non amp pages (BZNext.js)
    [BZNEXT-9114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9114)
  - identify where we are using postman token on com and replace it with new token (BZNext.js)
    [BZNEXT-9123](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9123)
  - add previous offerings to platform pages (BZNext.js)
    [BZNEXT-9136](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9136)
  - remove app links and links table from the footer on india app (INDIA)
    [INDIA-8955](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8955)
  - update latest news widget on india article sidebar (INDIA)
    [INDIA-8964](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8964)
  - money article page design update (MNY)
    [MNY-9040](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9040)
  - offering carousel slider not working (MNY)
    [MNY-9149](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9149)
  - update ui for sublist rendering for money blocks (MNY)
    [MNY-9358](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9358)

# v2.11.0

## Feature
  - check global article seo integration (BZFE)
    [BZFE-6721](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6721)
  - sponsored content infinite loads below article (BZFE)
    [BZFE-6921](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6921)
  - launch peoples choice awards for link building (MNY)
    [MNY-6809](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6809)
  - app marketing components (BZNext.js)
    [BZNEXT-7455](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7455)
  - refferer ad hide (BZNext.js)
    [BZNEXT-7689](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7689)
  - home page quick links (BZNext.js)
    [BZNEXT-8225](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8225)
  - insider trades migration and improvements (BZNext.js)
    [BZNEXT-8476](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8476)
  - translate node (BZNext.js)
    [BZNEXT-8781](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8781)
  - added recommended articles unit on com (BZNext.js)
    [BZNEXT-8784](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8784)
  - add automated report (BZNext.js)
    [BZNEXT-8829](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8829)
  - every story that matters around the web widget (BZNext.js)
    [BZNEXT-8858](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8858)
  - add wp preview (MNY)
    [MNY-640](https://gitlab.benzinga.io/benzinga/fusion/-/issues/640)
  - translate auth topbar watchlist components (BZFE)
    [BZFE-5238](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5238)
  - change article headline font size and weight on mobile (BZFE)
    [BZFE-6127](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6127)
  - quote page seo stat boxes (BZFE)
    [BZFE-6133](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6133)
  - standard template for company listing pages alts sponsored verified (BZFE)
    [BZFE-6136](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6136)
  - upvote downvote (BZFE)
    [BZFE-6185](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6185)
  - nft hubpage (BZFE)
    [BZFE-6242](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6242)
  - automated review sections (BZFE)
    [BZFE-6372](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6372)
  - add heatmap and 100x options calculator to menu (UI.js)
    [UI-6476](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6476)

## Bug
  - ignore rum errors for gsi logger origin (BZFE)
    [BZFE-5773](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5773)
  - dig into next memory leak (BZFE)
    [BZFE-6416](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6416)
  - canonical of undefined (BZFE)
    [BZFE-6535](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6535)
  - fix meta props issue (BZFE)
    [BZFE-6543](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6543)
  - handle 404 on crypto coin (BZFE)
    [BZFE-6556](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6556)
  - benzinga analyst ratings below article bug (BZFE)
    [BZFE-6560](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6560)
  - clicking ipo on homepage results in crash (BZFE)
    [BZFE-6572](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6572)
  - fix analyst redirect login (BZFE)
    [BZFE-6575](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6575)
  - debug missing action button text (BZFE)
    [BZFE-6599](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6599)
  - bug w missing price change percent change (BZFE)
    [BZFE-6609](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6609)
  - fix gpt slot error (BZFE)
    [BZFE-6641](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6641)
  - find and fix amp css important issue next (BZFE)
    [BZFE-6664](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6664)
  - decode html entities (BZFE)
    [BZFE-6717](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6717)
  - some amp pages missing nid in url next (BZFE)
    [BZFE-6726](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6726)
  - duplicate story in news template feed issue (BZFE)
    [BZFE-6736](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6736)
  - stories not showing up on quotes page profile tab (BZFE)
    [BZFE-6748](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6748)
  - issue with pre tag width (BZFE)
    [BZFE-6787](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6787)
  - missing crypto ticker movement (BZFE)
    [BZFE-6796](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6796)
  - fix quote tickers (BZFE)
    [BZFE-6797](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6797)
  - body contents missing for advertiser disclosure (BZFE)
    [BZFE-6806](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6806)
  - broken image no image for dental care advantage (BZFE)
    [BZFE-6807](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6807)
  - debug trader page (BZFE)
    [BZFE-6817](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6817)
  - master not building previous merge issue (BZFE)
    [BZFE-6818](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6818)
  - blurry image fix dpi configuration issues (BZFE)
    [BZFE-6821](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6821)
  - fix crypto com image (BZFE)
    [BZFE-6823](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6823)
  - fix missing tickers bug (BZFE)
    [BZFE-6828](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6828)
  - add no results check on popular stories (BZFE)
    [BZFE-6832](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6832)
  - fix recharts bug (BZFE)
    [BZFE-6855](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6855)
  - validate channel params (BZFE)
    [BZFE-6867](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6867)
  - fix crypto page broken images (BZFE)
    [BZFE-6871](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6871)
  - deduplicate stories on homepage (BZFE)
    [BZFE-6877](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6877)
  - fix linting err (BZFE)
    [BZFE-6887](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6887)
  - debug analyst rating date range selection bug (BZFE)
    [BZFE-6896](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6896)
  - debug captial rate calculator bug (BZFE)
    [BZFE-6898](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6898)
  - fix product table smaller size layouts (BZFE)
    [BZFE-6911](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6911)
  - bug w crypto landing page where watchlist was (BZFE)
    [BZFE-6924](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6924)
  - fix calendarwidget earnings request (BZFE)
    [BZFE-6939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6939)
  - fix and improved storybook deployment (BZFE)
    [BZFE-6951](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6951)
  - quote page profile broken canocical (BZFE)
    [BZFE-6963](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6963)
  - debug article draft mode crash (BZFE)
    [BZFE-6964](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6964)
  - fix ad disclosure overlay issue on larger phones (BZFE)
    [BZFE-6978](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6978)
  - in article earnings calendar showing wrong date from (BZFE)
    [BZFE-7002](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7002)
  - trade ideas page bz bull image not loading (BZFE)
    [BZFE-7009](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7009)
  - article page draft crash (BZFE)
    [BZFE-7032](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7032)
  - fix pagetracking replace history so utm parameters work (BZFE)
    [BZFE-7034](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7034)
  - fixes to analytics events (BZFE)
    [BZFE-7043](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7043)
  - debug pagetracking event on sponsored content load (BZFE)
    [BZFE-7051](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7051)
  - quote page fundamentals info table weird layout shift (BZFE)
    [BZFE-7057](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7057)
  - debug parsely event ignore contributed move trending above taboola (BZFE)
    [BZFE-7058](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7058)
  - fix sharebuttons import (BZFE)
    [BZFE-7059](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7059)
  - fixes to page tracking scroll update contributed content flag fitler remove taboola (BZFE)
    [BZFE-7063](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7063)
  - style bug on quote page in the quote header (BZFE)
    [BZFE-7075](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7075)
  - missing calendar on the calendar page (BZFE)
    [BZFE-7079](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7079)
  - bz bull logo loading issues (BZFE)
    [BZFE-7102](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7102)
  - load news cache re validation issue (BZNext.js)
    [BZNEXT-7104](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7104)
  - debug image not available immediately in content internal (BZNext.js)
    [BZNEXT-7113](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7113)
  - quote calendars not visible on page load only on tab change (BZNext.js)
    [BZNEXT-7148](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7148)
  - validate earnings data on quote pages (BZNext.js)
    [BZNEXT-7149](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7149)
  - trade ideas submit comment throws error (BZNext.js)
    [BZNEXT-7161](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7161)
  - bug when searching for tickers in trade idea portal (BZNext.js)
    [BZNEXT-7164](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7164)
  - latest briefs exclusives news not loading (BZNext.js)
    [BZNEXT-7170](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7170)
  - fixed bug with trade ideas page not loading (BZNext.js)
    [BZNEXT-7173](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7173)
  - fix taxonomy lookup (BZNext.js)
    [BZNEXT-7180](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7180)
  - handle pro only articles properly for amp (BZNext.js)
    [BZNEXT-7186](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7186)
  - fix amp pages (BZNext.js)
    [BZNEXT-7187](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7187)
  - fixed login not working (BZNext.js)
    [BZNEXT-7189](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7189)
  - follow up from resolve fix taxonomy lookup (BZNext.js)
    [BZNEXT-7198](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7198)
  - missing calendars on the calendar pages (BZNext.js)
    [BZNEXT-7209](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7209)
  - trade ideas does not load more by default on com (BZNext.js)
    [BZNEXT-7213](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7213)
  - trade ideas timestamp undefined on firefox (BZNext.js)
    [BZNEXT-7218](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7218)
  - bug no results for analyst ratings for spy or any etf crypto (BZNext.js)
    [BZNEXT-7223](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7223)
  - fixes to news template (BZNext.js)
    [BZNEXT-7238](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7238)
  - disable infinite scroll if bot detected next (BZNext.js)
    [BZNEXT-7252](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7252)
  - bug w non html widgets in premium secondary placehoders (MNY)
    [MNY-6899](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6899)
  - fix missing campaign display in placeholder cleanup code (MNY)
    [MNY-6916](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6916)
  - patch xss attacks (BZNext.js)
    [BZNEXT-6986](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6986)
  - duplicate articles or if there is a display issue https www benzinga com quote ua (BZNext.js)
    [BZNEXT-6990](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6990)
  - debug dupicate stories on category page forex (BZNext.js)
    [BZNEXT-7248](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7248)
  - fix html render issue aggrid (BZNext.js)
    [BZNEXT-7275](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7275)
  - fix loading draft crash (BZNext.js)
    [BZNEXT-7296](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7296)
  - fix missing description on quotes pages (BZNext.js)
    [BZNEXT-7301](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7301)
  - benzinga com bug fixes (BZNext.js)
    [BZNEXT-7309](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7309)
  - analyst rating calendar fix logged out height get alert (BZNext.js)
    [BZNEXT-7314](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7314)
  - duplicate articles on topic page featured recent news (BZNext.js)
    [BZNEXT-7323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7323)
  - redo duplicate articles on topic page featured recent news (BZNext.js)
    [BZNEXT-7323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7323)
  - fix sanitize html in widgets (BZNext.js)
    [BZNEXT-7341](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7341)
  - cannabis dashboard page errors (BZNext.js)
    [BZNEXT-7368](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7368)
  - calender server side date range issues (BZNext.js)
    [BZNEXT-7386](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7386)
  - yield investments (BZNext.js)
    [BZNEXT-7408](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7408)
  - no available data for earnings calendarswidget on home page (BZNext.js)
    [BZNEXT-7440](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7440)
  - amp pages do not properly handle image html for campaignify (BZNext.js)
    [BZNEXT-7441](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7441)
  - 404 error on slug routes (BZNext.js)
    [BZNEXT-7445](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7445)
  - fix sanitization issues (BZNext.js)
    [BZNEXT-7446](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7446)
  - 503 error (BZNext.js)
    [BZNEXT-7450](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7450)
  - ag grid warnings (BZNext.js)
    [BZNEXT-7453](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7453)
  - ic fxies (BZNext.js)
    [BZNEXT-7470](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7470)
  - recent is misspelled in the recent m a next (BZNext.js)
    [BZNEXT-7471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7471)
  - overlay style issues on dot com (BZNext.js)
    [BZNEXT-7472](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7472)
  - date range on analyst ratings bug (BZNext.js)
    [BZNEXT-7473](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7473)
  - benzinga briefs making too many requests on end reached (BZNext.js)
    [BZNEXT-7477](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7477)
  - core ui tooltip bugs (BZNext.js)
    [BZNEXT-7478](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7478)
  - quote page sponsored content bugs (BZNext.js)
    [BZNEXT-7498](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7498)
  - quote page double ad layout issue (BZNext.js)
    [BZNEXT-7499](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7499)
  - debug press release failing to load (BZNext.js)
    [BZNEXT-7500](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7500)
  - fix draft loading issue (BZNext.js)
    [BZNEXT-7504](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7504)
  - fix ticker hover wrong ticker on grid sort (BZNext.js)
    [BZNEXT-7507](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7507)
  - fix 404 crash (BZNext.js)
    [BZNEXT-7516](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7516)
  - calendar invalid date api request (BZNext.js)
    [BZNEXT-7520](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7520)
  - bug search on site broken (BZNext.js)
    [BZNEXT-7521](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7521)
  - cleanup quote data display to be consistent accross components (BZNext.js)
    [BZNEXT-7524](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7524)
  - tickerpopup many finage crypto requests being made (BZNext.js)
    [BZNEXT-7531](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7531)
  - remove amp url for pro only headlines (BZNext.js)
    [BZNEXT-7532](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7532)
  - alternative investments listing pages design updates (BZNext.js)
    [BZNEXT-7536](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7536)
  - cve shows three six months buy ratings but the consensus rating shows neutral (BZNext.js)
    [BZNEXT-7538](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7538)
  - fix sponsored posts limit (BZNext.js)
    [BZNEXT-7549](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7549)
  - recent tab fix (BZNext.js)
    [BZNEXT-7555](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7555)
  - follow up from resolve fix ticker hover wrong ticker on grid sort (BZNext.js)
    [BZNEXT-7587](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7587)
  - article page crashing (BZNext.js)
    [BZNEXT-7590](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7590)
  - header ad unit at top of page above drop down bars (BZNext.js)
    [BZNEXT-7607](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7607)
  - fix account hijacking vulnerability (BZNext.js)
    [BZNEXT-7617](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7617)
  - embedded video display issue (BZNext.js)
    [BZNEXT-7618](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7618)
  - debug missing dividend yield (BZNext.js)
    [BZNEXT-7620](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7620)
  - full tweets are not showing up in the post (BZNext.js)
    [BZNEXT-7622](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7622)
  - quote page fix (BZNext.js)
    [BZNEXT-7625](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7625)
  - debug missing image (BZNext.js)
    [BZNEXT-7626](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7626)
  - ticker brk a and brk b are not returning search results or quote pages on the website (BZNext.js)
    [BZNEXT-7629](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7629)
  -  tickers are missing from earnings calendar (BZNext.js)
    [BZNEXT-7633](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7633)
  - 404 pages triggering pageviews they should not (BZNext.js)
    [BZNEXT-7647](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7647)
  - fix quote page ad wrapping on newline (BZNext.js)
    [BZNEXT-7649](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7649)
  - fix white space issue on article calendar widget (BZNext.js)
    [BZNEXT-7659](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7659)
  - cannot open this link https www benzinga com quote ints should show a proper page message (BZNext.js)
    [BZNEXT-7690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7690)
  - iglb quote page not working (BZNext.js)
    [BZNEXT-7690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7690)
  - article quotes fail catch (BZNext.js)
    [BZNEXT-7698](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7698)
  - reflected xss on com redirect query param (BZNext.js)
    [BZNEXT-7699](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7699)
  - fix search bar aseventcapture (BZNext.js)
    [BZNEXT-7700](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7700)
  - xss in ideas comments (BZNext.js)
    [BZNEXT-7703](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7703)
  - fix html injection issue (BZNext.js)
    [BZNEXT-7713](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7713)
  - fix ticker exchange type issue (BZNext.js)
    [BZNEXT-7741](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7741)
  - core ui collapse component bugs (BZNext.js)
    [BZNEXT-7754](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7754)
  - fix primis render for mobile (BZNext.js)
    [BZNEXT-7758](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7758)
  - bug too many sponsored posts in latest hp section (BZNext.js)
    [BZNEXT-7775](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7775)
  - fix missing sticky nav footer on money (BZNext.js)
    [BZNEXT-7779](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7779)
  - individual analyst rating routing broken (BZNext.js)
    [BZNEXT-7780](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7780)
  - quote card forward yield bug (BZNext.js)
    [BZNEXT-7781](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7781)
  - analyst ratings table analyst styling issues (BZNext.js)
    [BZNEXT-7782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7782)
  - follow up from resolve quote card forward yield bug (BZNext.js)
    [BZNEXT-7783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7783)
  - for quote pages under the press releases tab no prs are available (BZNext.js)
    [BZNEXT-7787](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7787)
  - amp pages twitter share issues (BZNext.js)
    [BZNEXT-7797](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7797)
  - quote page missing analyst ratings (BZNext.js)
    [BZNEXT-7798](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7798)
  - fix mobile quote page layout bug (BZNext.js)
    [BZNEXT-7799](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7799)
  - search ux issues (BZNext.js)
    [BZNEXT-7800](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7800)
  - debug geo targeted widgets in advanced campaigns (BZNext.js)
    [BZNEXT-7801](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7801)
  - debug missing movers (BZNext.js)
    [BZNEXT-7802](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7802)
  - quote page bug fixes (BZNext.js)
    [BZNEXT-7805](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7805)
  - add back movers api wrapper (BZNext.js)
    [BZNEXT-7828](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7828)
  - fix amp disallowed attribute or attribute value present in html tag (BZNext.js)
    [BZNEXT-7832](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7832)
  - fix image paths on homepage to avoid blurry images (BZNext.js)
    [BZNEXT-7840](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7840)
  - movers api errors (BZNext.js)
    [BZNEXT-7843](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7843)
  - bug in the dividend yield forward value (BZNext.js)
    [BZNEXT-7849](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7849)
  - alternative investments section incorrect news on the home page (BZNext.js)
    [BZNEXT-7850](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7850)
  - home page issue sponsored post should not be there on the popular side it should be there only (BZNext.js)
    [BZNEXT-7851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7851)
  - change sponsored content query on home page (BZNext.js)
    [BZNEXT-7856](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7856)
  - missing popular stories on home page (BZNext.js)
    [BZNEXT-7857](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7857)
  - movers request failing on movers page (BZNext.js)
    [BZNEXT-7860](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7860)
  - iframe widget rendering incognito bug (FUSION)
    [FUSION-6435](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6435)
  - mobile navigation menu overlapping elements (BZNext.js)
    [BZNEXT-7866](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7866)
  - article get quotes error typeerror cannot read properties of undefined reading tostring at (BZNext.js)
    [BZNEXT-7896](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7896)
  - infinite requests being made on ticker hover (BZNext.js)
    [BZNEXT-7901](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7901)
  - movers request failing again (BZNext.js)
    [BZNEXT-7908](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7908)
  - fix disable optinmonster for page (BZNext.js)
    [BZNEXT-7909](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7909)
  - fix newsfeed jumping at india (BZNext.js)
    [BZNEXT-7918](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7918)
  - articles from psychedelics section landing to 404 error page (BZNext.js)
    [BZNEXT-7930](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7930)
  - amp pages not indexing with error an amp component script tag is present but unused (BZNext.js)
    [BZNEXT-7939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7939)
  - fix facebook share button via amp (BZNext.js)
    [BZNEXT-7941](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7941)
  - preload zack script (BZNext.js)
    [BZNEXT-7951](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7951)
  - prs going out without a primary photo (BZNext.js)
    [BZNEXT-7952](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7952)
  - ticker quote pages earnings ratings missing data (BZNext.js)
    [BZNEXT-7955](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7955)
  - fix query parameter format for campaignify utm campaign (BZNext.js)
    [BZNEXT-7958](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7958)
  - trade idea submission bug (BZNext.js)
    [BZNEXT-7962](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7962)
  - zackad fix (BZNext.js)
    [BZNEXT-7998](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7998)
  - adjust logic for campaign layout to handle non paragraphs (BZNext.js)
    [BZNEXT-8017](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8017)
  - add posts by state block to approved dangerous html blocks (BZNext.js)
    [BZNEXT-8028](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8028)
  - after logging in user is redirected to undefined page (BZNext.js)
    [BZNEXT-8029](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8029)
  - ticker component requesting incorrect api on article pages (BZNext.js)
    [BZNEXT-8039](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8039)
  - home page carousel on mobile can not reach the end when scrolling (BZNext.js)
    [BZNEXT-8059](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8059)
  - analyst ratings analyst score not displaying correctly on analyst stock ratings page (BZNext.js)
    [BZNEXT-8061](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8061)
  - xss attack on calendar link (BZNext.js)
    [BZNEXT-8075](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8075)
  - revert of analyst ratings analyst score not displaying correctly on analyst stock ratings page (BZNext.js)
    [BZNEXT-8089](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8089)
  - missing analyst ratings scores on non logged in page (BZNext.js)
    [BZNEXT-8102](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8102)
  - public com charts quote page integration 2 (BZNext.js)
    [BZNEXT-8114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8114)
  - incorrect link to quote page on m a serversidecalendar (BZNext.js)
    [BZNEXT-8131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8131)
  - fix the api endpoint for getting ticker reports (BZNext.js)
    [BZNEXT-8144](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8144)
  - crypto bitcoin page errors when scroll to the bottom (BZNext.js)
    [BZNEXT-8172](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8172)
  - quote delayed safety (BZNext.js)
    [BZNEXT-8195](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8195)
  - fix various bugs that arose after homepage cms task (BZNext.js)
    [BZNEXT-8198](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8198)
  - impressions delivery issue rocket mortgage negative keyword targeting (BZNext.js)
    [BZNEXT-8205](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8205)
  - dfp id fix (BZNext.js)
    [BZNEXT-8216](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8216)
  - yield investments page carousel style issues (BZNext.js)
    [BZNEXT-8223](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8223)
  - serversidecalendar incorrect quote on tickercard (BZNext.js)
    [BZNEXT-8231](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8231)
  - kill quote logger (BZNext.js)
    [BZNEXT-8236](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8236)
  - fix quote divindend cc response (BZNext.js)
    [BZNEXT-8244](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8244)
  - fix nft page crash (BZNext.js)
    [BZNEXT-8245](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8245)
  - auto page refresh bug (BZNext.js)
    [BZNEXT-8248](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8248)
  - fix terms err (BZNext.js)
    [BZNEXT-8262](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8262)
  - earnings news page oddly styled (BZNext.js)
    [BZNEXT-8265](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8265)
  - incorrect upcoming dividend ex date wording (BZNext.js)
    [BZNEXT-8276](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8276)
  - fix issue with time offset for quote page ex date notice (BZNext.js)
    [BZNEXT-8285](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8285)
  - bug stories not appearing on quotes page or on news tab (BZNext.js)
    [BZNEXT-8289](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8289)
  - issues with primis player receiving too many calls per page load effecting performance and (BZNext.js)
    [BZNEXT-8292](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8292)
  - expert ideas sidebar component right border is missing (BZNext.js)
    [BZNEXT-8300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8300)
  - missing podcasts list in sidebar for latest briefs and exclusives tabs (BZNext.js)
    [BZNEXT-8306](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8306)
  - missing google news button image on benzinga india article pages (BZNext.js)
    [BZNEXT-8314](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8314)
  - fix invalid date in authorcontent box (BZNext.js)
    [BZNEXT-8315](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8315)
  - debug issue around 10k logs each day for primary image probe error (BZNext.js)
    [BZNEXT-8321](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8321)
  - benzinga s top initiations article redirects to home page (BZNext.js)
    [BZNEXT-8331](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8331)
  - analyst name column is being hidden when you go back after clicking the analyst ratings (BZNext.js)
    [BZNEXT-8342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8342)
  - issue with alphastream events not being logged (BZNext.js)
    [BZNEXT-8376](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8376)
  - benzinga com quick ticker search selection not keyword (BZNext.js)
    [BZNEXT-8386](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8386)
  - landing topic partner etc pages published date (BZNext.js)
    [BZNEXT-8396](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8396)
  - item in analyst ratings table is showing nan (BZNext.js)
    [BZNEXT-8399](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8399)
  - missing clientref and no event sent when article is opened from another article (BZNext.js)
    [BZNEXT-8402](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8402)
  - fix blurry downsized images for bzimage on homepage (BZNext.js)
    [BZNEXT-8418](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8418)
  - crypto logo on bz india leads to incorrect page (BZNext.js)
    [BZNEXT-8426](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8426)
  - stockfundamentals on quote page has missing fields (BZNext.js)
    [BZNEXT-8427](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8427)
  - fix amp issue disallowed attribute or attribute value present in html tag (BZNext.js)
    [BZNEXT-8433](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8433)
  - fix bz deploy (BZNext.js)
    [BZNEXT-8449](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8449)
  - watchlist tab in latest news list not getting the correct data (BZNext.js)
    [BZNEXT-8451](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8451)
  - mobile navigation header cls issue (BZNext.js)
    [BZNEXT-8465](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8465)
  - menu layout issue on collapsed dropdown (BZNext.js)
    [BZNEXT-8469](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8469)
  - article amp pages not loading (BZNext.js)
    [BZNEXT-8474](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8474)
  - minor bug fixes on dot com post nx update (BZNext.js)
    [BZNEXT-8481](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8481)
  - fix issue with ssr and asset sale (BZNext.js)
    [BZNEXT-8485](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8485)
  - fixes to page render (BZNext.js)
    [BZNEXT-8488](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8488)
  - amc missing logo bug (BZNext.js)
    [BZNEXT-8496](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8496)
  - merger text display fixes (BZNext.js)
    [BZNEXT-8498](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8498)
  - missing client ref (BZNext.js)
    [BZNEXT-8502](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8502)
  - missing content feed expert ideas in sidebar (BZNext.js)
    [BZNEXT-8506](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8506)
  - handle full url canonical path (BZNext.js)
    [BZNEXT-8514](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8514)
  - widgets on india stories homepage sidebar broken (BZNext.js)
    [BZNEXT-8530](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8530)
  - error cannot read properties of undefined reading logourl (BZNext.js)
    [BZNEXT-8565](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8565)
  - sponsored content in latest not property attributed as sponsored partner (BZNext.js)
    [BZNEXT-8570](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8570)
  - duplicate table in earnings article (BZNext.js)
    [BZNEXT-8580](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8580)
  - home page and crypto page issues (BZNext.js)
    [BZNEXT-8595](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8595)
  - get report button is being shown for unsupported tickers (BZNext.js)
    [BZNEXT-8597](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8597)
  - optimizing react lazy suspense for cls (BZNext.js)
    [BZNEXT-8610](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8610)
  - fix bz india nextconfig article path regex (BZNext.js)
    [BZNEXT-8619](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8619)
  - content sizing spacing issues with b2b campaignify ads within articles (BZNext.js)
    [BZNEXT-8623](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8623)
  - prevent primis player on sponsored articles (BZNext.js)
    [BZNEXT-8631](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8631)
  - handle conference call end timings (BZNext.js)
    [BZNEXT-8643](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8643)
  - fix next config analyze (BZNext.js)
    [BZNEXT-8645](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8645)
  - hydration issue (BZNext.js)
    [BZNEXT-8650](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8650)
  - dividend calendar title says dividends as of april 10th and the records shown are of 04 11 2023 (BZNext.js)
    [BZNEXT-8684](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8684)
  - conference call calendar the default date selected should be today (BZNext.js)
    [BZNEXT-8685](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8685)
  - estimates data issue on the quotes page earnings calendar (BZNext.js)
    [BZNEXT-8686](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8686)
  - earnings calendar the calendar loads the previous day s records even though the date selected (BZNext.js)
    [BZNEXT-8687](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8687)
  - stock splits calendar records should be displayed based on ex date instead of announcement date (BZNext.js)
    [BZNEXT-8688](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8688)
  - most shorted stocks updated date is a strange format 20230324 (BZNext.js)
    [BZNEXT-8691](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8691)
  - alts listing page issues (BZNext.js)
    [BZNEXT-8692](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8692)
  - earnings calendar q a section at the bottom on the earnings calendar is showing wrong data (BZNext.js)
    [BZNEXT-8694](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8694)
  - the dividends calendar is having an issue displaying company names when the name is big (BZNext.js)
    [BZNEXT-8703](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8703)
  - dfp loading error (BZNext.js)
    [BZNEXT-8704](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8704)
  - development request fixing the analyst ratings section on the quotes page to not show prior pt (BZNext.js)
    [BZNEXT-8706](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8706)
  - issues with article primary image incorrect values for alt (BZNext.js)
    [BZNEXT-8710](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8710)
  - quote page hydration redo (BZNext.js)
    [BZNEXT-8715](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8715)
  - admgid bugs console (BZNext.js)
    [BZNEXT-8736](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8736)
  - crypto homepage is not loading from article pages (BZNext.js)
    [BZNEXT-8737](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8737)
  - benzinga logo flashes benzinga holiday logo (BZNext.js)
    [BZNEXT-8743](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8743)
  - typeerror cannot read properties of undefined reading innerhtml (BZNext.js)
    [BZNEXT-8747](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8747)
  - debug issue w twitter tweets not rendering (BZNext.js)
    [BZNEXT-8748](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8748)
  - quote page cls issue with tabs (BZNext.js)
    [BZNEXT-8750](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8750)
  - news editorial get name published instead of email and add writers in new cms (BZNext.js)
    [BZNEXT-8751](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8751)
  - primis player not loading on us traffic (BZNext.js)
    [BZNEXT-8753](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8753)
  - primis player not loading on us traffic 2 (BZNext.js)
    [BZNEXT-8753](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8753)
  - admgid referenceerror document is not defined (BZNext.js)
    [BZNEXT-8754](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8754)
  - postedin at array map anonymous (BZNext.js)
    [BZNEXT-8755](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8755)
  - bug 404 content not found (BZNext.js)
    [BZNEXT-8763](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8763)
  - switching symbols on quote page not updating price correctly (BZNext.js)
    [BZNEXT-8768](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8768)
  - duplicate item in exclusives carousel (BZNext.js)
    [BZNEXT-8786](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8786)
  - fix canonical duplication bug w causing analytic issues on infinite scroll (BZNext.js)
    [BZNEXT-8791](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8791)
  - amp issue referenced amp url is self canonical amp (BZNext.js)
    [BZNEXT-8793](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8793)
  - primis issue (BZNext.js)
    [BZNEXT-8802](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8802)
  - articles on mobile laptop safari loading and then showing uh oh undefined (BZNext.js)
    [BZNEXT-8810](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8810)
  - issue on amp pages (BZNext.js)
    [BZNEXT-8816](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8816)
  - primis mobile issue (BZNext.js)
    [BZNEXT-8828](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8828)
  - article mobile layout overflow issue (BZNext.js)
    [BZNEXT-8838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8838)
  - the economic calendar and conference call calendar pages to show the latest date (BZNext.js)
    [BZNEXT-8851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8851)
  - fix ga4 startup (BZNext.js)
    [BZNEXT-8867](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8867)
  - linkgroupblock links are returning an empty array (BZNext.js)
    [BZNEXT-8869](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8869)
  - sponsored content to appear in search on quote page primary ticker (BZNext.js)
    [BZNEXT-8873](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8873)
  - canonical url mismatch redirect (BZNext.js)
    [BZNEXT-8878](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8878)
  - some calendars have incorrect titles on weekends (BZNext.js)
    [BZNEXT-8901](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8901)
  - quote page news section is missing (BZNext.js)
    [BZNEXT-8909](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8909)
  - style update to every story that matters around the web widget (BZNext.js)
    [BZNEXT-8912](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8912)
  - follow up from resolve quote page news section is missing (BZNext.js)
    [BZNEXT-8921](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8921)
  - 404 error on forex page (BZNext.js)
    [BZNEXT-8923](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8923)
  - self referencing canoncials on analyst ratings page others (BZNext.js)
    [BZNEXT-8926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8926)
  - sidebar error (BZNext.js)
    [BZNEXT-8939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8939)
  - amp footer header issue (BZNext.js)
    [BZNEXT-8940](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8940)
  - analyst rating filter bug (BZNext.js)
    [BZNEXT-8949](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8949)
  - every story that matters around the web widget not loading links on slug route (BZNext.js)
    [BZNEXT-8953](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8953)
  - an article error typeerror cannot read properties of null reading canonicalpath (BZNext.js)
    [BZNEXT-8971](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8971)
  - responsive ic (BZNext.js)
    [BZNEXT-8978](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8978)
  - sec filings hydration issue (BZNext.js)
    [BZNEXT-8986](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8986)
  - sec filings 500 error on navigation (BZNext.js)
    [BZNEXT-8987](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8987)
  - sec filings 500 error on navigation existing issue (BZNext.js)
    [BZNEXT-8987](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8987)
  - fix translation espania bar (BZNext.js)
    [BZNEXT-9000](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9000)
  - dividend the table is returning nan no data (BZNext.js)
    [BZNEXT-9009](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9009)
  - update twitter block (BZNext.js)
    [BZNEXT-9020](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9020)
  - canonical url too many redirects issue (BZNext.js)
    [BZNEXT-9060](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9060)
  - wrong canonical on a bunch of pages (BZNext.js)
    [BZNEXT-9067](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9067)
  - wrong canonical tag on fintech article (BZNext.js)
    [BZNEXT-9069](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9069)
  - noindex on old sec filings in content (BZNext.js)
    [BZNEXT-9071](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9071)
  - campaignify block not rendering in article (BZNext.js)
    [BZNEXT-9084](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9084)
  - dividend faq has incorrect stock name in the answer to the question when is the next xxx (BZNext.js)
    [BZNEXT-9097](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9097)
  - author pages are collecting a lot of errors (BZNext.js)
    [BZNEXT-9099](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9099)
  - press release author issues (BZNext.js)
    [BZNEXT-9102](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9102)
  - load more stories button (BZNext.js)
    [BZNEXT-9107](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9107)
  - author page incorrect data (BZNext.js)
    [BZNEXT-9108](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9108)
  - author page article image sizing issues (BZNext.js)
    [BZNEXT-9111](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9111)
  - analyst ratings analyst page undefined error (BZNext.js)
    [BZNEXT-9125](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9125)
  - alternative investments initial news is incorrect (BZNext.js)
    [BZNEXT-9127](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9127)
  - fix custom dimensions next (BZFE)
    [BZFE-5835](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5835)
  - twitter embeds aren t formatting next (BZFE)
    [BZFE-5939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5939)
  - featured offerings blocks page scroll on mobile (BZFE)
    [BZFE-6042](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6042)
  - article content being stripped when p wraps ul deubg and fix next (BZFE)
    [BZFE-6077](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6077)
  - some of the ticker pages are giving error (BZFE)
    [BZFE-6101](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6101)
  - pagetypechange (BZFE)
    [BZFE-6115](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6115)
  - stock splits missing title (BZFE)
    [BZFE-6134](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6134)
  - video duration incorrect bug (BZFE)
    [BZFE-6145](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6145)
  - fix primis video overlay issue (BZFE)
    [BZFE-6152](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6152)
  - debug author editor analytics requests (BZFE)
    [BZFE-6156](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6156)
  - resolve author editor authentication issues failover for article publication (BZFE)
    [BZFE-6175](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6175)
  - fix overlay issue (BZFE)
    [BZFE-6181](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6181)
  - allow render of empty tags in article (BZFE)
    [BZFE-6200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6200)
  - fix quote exchange bug (BZFE)
    [BZFE-6206](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6206)
  - fix br html render (BZFE)
    [BZFE-6216](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6216)
  - home page multiple render bug (BZFE)
    [BZFE-6226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6226)
  - fix calendar date format (BZFE)
    [BZFE-6236](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6236)
  - scrolling bug on watch page (BZFE)
    [BZFE-6243](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6243)
  - layout login bug fixes (BZFE)
    [BZFE-6333](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6333)
  - homepage tabs do not save history (BZFE)
    [BZFE-6345](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6345)
  - amp validation bug crash (BZFE)
    [BZFE-6364](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6364)
  - analyst ratings page crash (BZFE)
    [BZFE-6369](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6369)
  - exclusives carousel missing slider buttons (BZFE)
    [BZFE-6371](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6371)
  - extend unescape html (BZFE)
    [BZFE-6374](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6374)
  - article br issue (BZFE)
    [BZFE-6375](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6375)
  - handle html entities (BZFE)
    [BZFE-6376](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6376)
  - quote page crashes (BZFE)
    [BZFE-6377](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6377)
  - article error notfounderror failed to execute insertbefore on node the node before which the (BZFE)
    [BZFE-6392](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6392)
  - article error an article error typeerror cannot read property err of undefined (BZFE)
    [BZFE-6413](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6413)
  - article error an article error typeerror cannot read property err of undefined 2 (BZFE)
    [BZFE-6413](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6413)
  - quote earnings page error typeerror cannot read property 0 of undefined (BZFE)
    [BZFE-6415](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6415)
  - amp error cannot read property err of undefined next (BZFE)
    [BZFE-6427](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6427)
  - article error sanitize error runtimeerror aborted oom next (BZFE)
    [BZFE-6428](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6428)
  - change ic default desktop size (BZFE)
    [BZFE-6449](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6449)
  - fix analyst stock ratings canonical url (BZFE)
    [BZFE-6457](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6457)
  - fix exclude automated (BZFE)
    [BZFE-6459](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6459)
  - quote page bugs (BZFE)
    [BZFE-6463](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6463)
  - fix default ad header height (BZFE)
    [BZFE-6496](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6496)
  - google auth fixes (BZFE)
    [BZFE-6505](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6505)

## Task
  - sandbox url as dotcom (BZFE)
    [BZFE-5244](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5244)
  - set default canonical next 2 (BZFE)
    [BZFE-5421](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5421)
  - typeerror cannot read property type of null (BZFE)
    [BZFE-6114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6114)
  - typeerror cannot read property type of null 2 (BZFE)
    [BZFE-6114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6114)
  - cleanup used css files from bz next (BZFE)
    [BZFE-6312](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6312)
  - crypto landing page improvements jose rodrigo safdiye (BZFE)
    [BZFE-6419](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6419)
  - cls fix for crypto widget (BZFE)
    [BZFE-6423](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6423)
  - acf featured products (BZFE)
    [BZFE-6439](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6439)
  - add live stream player to benzinga home page (BZFE)
    [BZFE-6475](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6475)
  - analyst ratings calendar success score (BZFE)
    [BZFE-6510](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6510)
  - translation bug mobile menu (BZFE)
    [BZFE-6511](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6511)
  - hide read in app button on sponsored posts (BZFE)
    [BZFE-6512](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6512)
  - primis live fixes (BZFE)
    [BZFE-6534](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6534)
  - fix primis live unit (BZFE)
    [BZFE-6536](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6536)
  - ad unit fixes (BZFE)
    [BZFE-6540](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6540)
  - use currency field on individual futures pages (BZFE)
    [BZFE-6545](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6545)
  - further ad debugging (BZFE)
    [BZFE-6546](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6546)
  - money header spacing issues (BZFE)
    [BZFE-6549](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6549)
  - product detail card fixes (BZFE)
    [BZFE-6550](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6550)
  - fix calendar news vs headline feeds (BZFE)
    [BZFE-6552](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6552)
  - futures news feed improvements (BZFE)
    [BZFE-6553](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6553)
  - ui ux improvements to simple movers widget (BZFE)
    [BZFE-6561](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6561)
  - integrate disclosure field on alt pages (BZFE)
    [BZFE-6568](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6568)
  - remove premium products iframe (BZFE)
    [BZFE-6569](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6569)
  - resolve missing faq block schema (BZFE)
    [BZFE-6587](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6587)
  - sponsored client page touchups (BZFE)
    [BZFE-6588](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6588)
  - update popular stories api (BZFE)
    [BZFE-6592](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6592)
  - setup dynamic geo targeting (BZFE)
    [BZFE-6594](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6594)
  - reenable onesignal for dotcom next (BZFE)
    [BZFE-6595](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6595)
  - impliment course carousel schema (BZFE)
    [BZFE-6597](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6597)
  - decrease space between top display unit and headline on mobile (BZFE)
    [BZFE-6601](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6601)
  - fix missing home insurance calculator data lot widget (BZFE)
    [BZFE-6603](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6603)
  - swap article page google news for apple news when a user is on mac next (BZFE)
    [BZFE-6604](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6604)
  - debug ic header configuration (BZFE)
    [BZFE-6610](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6610)
  - send next static assets to s3 for money undo (BZFE)
    [BZFE-6621](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6621)
  - configure rel sponsored anchor attribute for sponsored content (BZFE)
    [BZFE-6628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6628)
  - unusual options calendar seo (BZFE)
    [BZFE-6632](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6632)
  - navbar items bug (BZFE)
    [BZFE-6643](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6643)
  - add max image preview large to header for google discover next money (BZFE)
    [BZFE-6645](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6645)
  - debug missing earnings data article calendar (BZFE)
    [BZFE-6647](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6647)
  - add benzinga admin editorial button on login for editor writer admin (BZFE)
    [BZFE-6661](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6661)
  - fixes to fda calendar data display (BZFE)
    [BZFE-6662](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6662)
  - debug review card display text encoding bug (BZFE)
    [BZFE-6667](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6667)
  - fix missing review schema (BZFE)
    [BZFE-6668](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6668)
  - disable mixi for now (BZFE)
    [BZFE-6676](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6676)
  - add event action for for free alert click (BZFE)
    [BZFE-6677](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6677)
  - in article unit need 300x50 and 300x100 on mobile (BZFE)
    [BZFE-6678](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6678)
  - duplicate story on topic page issue (BZFE)
    [BZFE-6679](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6679)
  - handle variables in campaignify units (BZFE)
    [BZFE-6682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6682)
  - change the social links to individual social icons (BZFE)
    [BZFE-6685](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6685)
  - update next routing to handle no base slug artilcle urls (BZFE)
    [BZFE-6686](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6686)
  - adjust calendar widget date ranges for various use cases (BZFE)
    [BZFE-6687](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6687)
  - move mortgage rates page to dynamic slug route (BZFE)
    [BZFE-6688](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6688)
  - dynamic google ad size (BZFE)
    [BZFE-6690](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6690)
  - video missing above table of contents unavailable (BZFE)
    [BZFE-6694](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6694)
  - discover more course and load more button is missing (BZFE)
    [BZFE-6696](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6696)
  - workers compensation calculator is missing (BZFE)
    [BZFE-6697](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6697)
  - migration bug fixes (BZFE)
    [BZFE-6698](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6698)
  - missing toll free card (BZFE)
    [BZFE-6699](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6699)
  - revert broken ci (BZFE)
    [BZFE-6704](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6704)
  - kill iframe product showcase in the article pages (BZFE)
    [BZFE-6715](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6715)
  - unused footer in money (BZFE)
    [BZFE-6727](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6727)
  - update quote news to latest news page title (BZFE)
    [BZFE-6729](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6729)
  - need 300x250 ad slots on mobile ticker pages (BZFE)
    [BZFE-6740](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6740)
  - options to remove navigation bar footer (BZFE)
    [BZFE-6741](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6741)
  - hide securely thourgh flag (BZFE)
    [BZFE-6742](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6742)
  - more dynamic news template movers cleanup ad height fixes (BZFE)
    [BZFE-6747](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6747)
  - crypto add to watchlist on dotcom (BZFE)
    [BZFE-6756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6756)
  - add content to futures pages (BZFE)
    [BZFE-6757](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6757)
  - fix lint issues (BZFE)
    [BZFE-6761](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6761)
  - migrate to adjusted price targets and logos v2 on quotes page (BZFE)
    [BZFE-6762](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6762)
  - remove menu items (BZFE)
    [BZFE-6773](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6773)
  - add missing breadcrumb seo schema (BZFE)
    [BZFE-6776](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6776)
  - isolate stock losses gainers (BZFE)
    [BZFE-6777](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6777)
  - ask the expert block is broken (BZFE)
    [BZFE-6781](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6781)
  - analyst ratings ux improvements (BZFE)
    [BZFE-6782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6782)
  - homepage latest news unit w tabs by news type stocks and crypto and move watchlist news to (BZFE)
    [BZFE-6783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6783)
  - use adjusted price targets on analyst ratings page (BZFE)
    [BZFE-6792](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6792)
  - increate lazy offset trigger for taboola (BZFE)
    [BZFE-6793](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6793)
  - fix quotes bar search bar on scroll (BZFE)
    [BZFE-6794](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6794)
  - address target return advertising policy (BZFE)
    [BZFE-6795](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6795)
  - job listings section missing in right panel (BZFE)
    [BZFE-6805](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6805)
  - integrate alphastream (BZFE)
    [BZFE-6812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6812)
  - ticker hover link to quote pages (BZFE)
    [BZFE-6825](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6825)
  - load more on homepage (BZFE)
    [BZFE-6827](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6827)
  - automated attribute styles (BZFE)
    [BZFE-6833](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6833)
  - fix contentfeed loading indicator (BZFE)
    [BZFE-6836](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6836)
  - debug on page ctas (BZFE)
    [BZFE-6843](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6843)
  - configure option to allow dynamic ad height (BZFE)
    [BZFE-6844](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6844)
  - remove local storage from login (BZFE)
    [BZFE-6846](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6846)
  - resolve production runtime crash (BZFE)
    [BZFE-6847](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6847)
  - analyst ratings smart score column sort (BZFE)
    [BZFE-6849](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6849)
  - go link redirect pass url integration (BZFE)
    [BZFE-6851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6851)
  - fix cls change for crypto com unit (BZFE)
    [BZFE-6858](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6858)
  - analyze campaigns loading on new templates (BZFE)
    [BZFE-6859](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6859)
  - add hubspot signup ga event (BZFE)
    [BZFE-6861](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6861)
  - how to buy buttons do not function (BZFE)
    [BZFE-6862](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6862)
  - change exclusives to market moving exclusives on the home page (BZFE)
    [BZFE-6863](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6863)
  - analyst ratings analyst page (BZFE)
    [BZFE-6864](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6864)
  - crypto com coinlist units content not loading (BZFE)
    [BZFE-6868](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6868)
  - edit geo location for lemonade home (BZFE)
    [BZFE-6873](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6873)
  - add invest in watches to menu (BZFE)
    [BZFE-6885](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6885)
  - define more detailed click actions (BZFE)
    [BZFE-6895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6895)
  - search results pages title match keyword sorted by chronological (BZFE)
    [BZFE-6905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6905)
  - migrate options calculator to fusion (BZFE)
    [BZFE-6906](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6906)
  - earnings calendar sort by biggest rev est (BZFE)
    [BZFE-6908](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6908)
  - clear cache button (BZFE)
    [BZFE-6909](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6909)
  - migrate press releases page to contentfeed (BZFE)
    [BZFE-6910](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6910)
  - fix analyst rating parameter on quotes page (BZFE)
    [BZFE-6912](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6912)
  - leverage surrogate keys for better cache clearing policies (BZFE)
    [BZFE-6915](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6915)
  - change alignment of load buttons home page (BZFE)
    [BZFE-6926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6926)
  - options calendar seo subtitle (BZFE)
    [BZFE-6928](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6928)
  - sponsored post label style update (BZFE)
    [BZFE-6935](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6935)
  - technical seo quote page fixes (BZFE)
    [BZFE-6962](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6962)
  - alt hub pages on alt nav bar www benzinga com (BZFE)
    [BZFE-6965](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6965)
  - seo news title ranking tickers and company names (BZFE)
    [BZFE-6973](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6973)
  - update environment selection to use runtime env (BZFE)
    [BZFE-6974](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6974)
  - follow up from resolve bug w crypto landing page where watchlist was (BZFE)
    [BZFE-6976](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6976)
  - update event category name (BZFE)
    [BZFE-6977](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6977)
  - add client side validation to hubspot form (BZFE)
    [BZFE-6981](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6981)
  - infinite scroll articles next steps (BZFE)
    [BZFE-6982](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6982)
  - bug load more on crypto landing page broken (BZFE)
    [BZFE-6983](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6983)
  - fix missing filter buttons on calendars (BZFE)
    [BZFE-6984](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6984)
  - fix entity card image stretch (BZFE)
    [BZFE-6991](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6991)
  - setting up https with bz next (BZFE)
    [BZFE-6999](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6999)
  - follow up on please update the user sync (BZFE)
    [BZFE-7004](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7004)
  - remove benzinga box (BZFE)
    [BZFE-7006](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7006)
  - clean up remove unneeded duplicate assets (BZFE)
    [BZFE-7019](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7019)
  - update imageplacement component for alt title source proper usage (BZFE)
    [BZFE-7021](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7021)
  - integrate gravity form (BZFE)
    [BZFE-7022](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7022)
  - article footer rearrangement (BZFE)
    [BZFE-7033](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7033)
  - add content header footer below above layouts to calendar template (BZFE)
    [BZFE-7035](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7035)
  - follow up from resolve account dropdown improvements (BZFE)
    [BZFE-7044](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7044)
  - follow up from resolve migrate press releases page to contentfeed (BZFE)
    [BZFE-7045](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7045)
  - add vertical category author etc dfp tags to money site (BZFE)
    [BZFE-7050](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7050)
  - follow up from resolve search results pages title match keyword sorted by chronological (BZFE)
    [BZFE-7056](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7056)
  - removed deprecated components part of maintenance week (BZFE)
    [BZFE-7068](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7068)
  - benzinga com header symbol order (BZFE)
    [BZFE-7076](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7076)
  - issues w stored quote name not updating use quote endpoint over internal content data (BZFE)
    [BZFE-7077](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7077)
  - mobile article pages spacing below bullets (BZFE)
    [BZFE-7078](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7078)
  - small bz next improvements (BZFE)
    [BZFE-7080](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7080)
  - add registration page w redirect option (BZFE)
    [BZFE-7085](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7085)
  - remove press releases from quote news filtering options on dotcom (BZFE)
    [BZFE-7091](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7091)
  - make quote bar less janky (BZFE)
    [BZFE-7094](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7094)
  - follow up from resolve issues w stored quote name not updating use quote endpoint over (BZFE)
    [BZFE-7099](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7099)
  - sponsored content next steps for analytics (BZFE)
    [BZFE-7101](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7101)
  - allow searching from the ticker quote page on mobile without having to click back (BZM)
    [BZM-6128](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6128)
  - convert news app to expo eas in fusion and add squawk to it (BZM)
    [BZM-6394](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6394)
  - remove campaignify ads from article screen (BZM)
    [BZM-6733](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6733)
  - bz mobile app fix linting (BZM)
    [BZM-6879](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6879)
  - setup staging process to test in review tickets (BZM)
    [BZM-6957](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6957)
  - hide loading indicator for first page of news list (BZM)
    [BZM-7020](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7020)
  - ticker fb is no longer in use but can still be referenced in our system (BZNext.js)
    [BZNEXT-6490](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6490)
  - integrate alphastream (BZNext.js)
    [BZNEXT-6812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6812)
  - error formatimagecoreblock (BZNext.js)
    [BZNEXT-6944](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6944)
  - integrate gravity form (BZNext.js)
    [BZNEXT-7022](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7022)
  - add vertical category author etc dfp tags to money site (BZNext.js)
    [BZNEXT-7050](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7050)
  - follow up from resolve add content header footer below above layouts to calendar template (BZNext.js)
    [BZNEXT-7097](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7097)
  - adtoniq tag (BZNext.js)
    [BZNEXT-7110](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7110)
  - move seo exchange ticker to end of seo title (BZNext.js)
    [BZNEXT-7120](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7120)
  - alphastream follow ups (BZNext.js)
    [BZNEXT-7165](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7165)
  - improve keyword search ux (BZNext.js)
    [BZNEXT-7193](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7193)
  - implement frontend interface for self checkout (BZNext.js)
    [BZNEXT-7210](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7210)
  - remove photon library (BZNext.js)
    [BZNEXT-7217](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7217)
  - update display to default to created at (BZNext.js)
    [BZNEXT-7226](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7226)
  - lisa levin as automated content missing from recent news feed (BZNext.js)
    [BZNEXT-7229](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7229)
  - headline only articles on benzinga com (BZNext.js)
    [BZNEXT-7230](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7230)
  - diagnose cls increase w latest deployment (BZNext.js)
    [BZNEXT-7233](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7233)
  - debug image propertery on missing object (BZNext.js)
    [BZNEXT-7234](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7234)
  - customize platform details fields (BZNext.js)
    [BZNEXT-7244](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7244)
  - differences with the existing production (NewsdeskTools.js)
    [DESK-6840](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6840)
  - newsdesk postbox should handle query (NewsdeskTools.js)
    [DESK-6920](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6920)
  - fix fusion linter (Fusion)
    [Fusion-6067](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6067)
  - master branch broken (Fusion)
    [Fusion-6702](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6702)
  - reduce number of chunks being loaded (MNY)
    [MNY-5957](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5957)
  - money next migration (MNY)
    [MNY-6529](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6529)
  - money site optimizations (MNY)
    [MNY-6547](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6547)
  - migration bug fixes (MNY)
    [MNY-6567](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6567)
  - best stocks cleanup (MNY)
    [MNY-6576](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6576)
  - money migration updates (MNY)
    [MNY-6593](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6593)
  - reduce js filesize (MNY)
    [MNY-6636](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6636)
  - money ui fixes (MNY)
    [MNY-6753](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6753)
  - setup datadog apm for money (MNY)
    [MNY-6768](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6768)
  - money breadcrumbs (MNY)
    [MNY-6772](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6772)
  - money site product cms fixes (MNY)
    [MNY-6824](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6824)
  - fix broken money deployment (MNY)
    [MNY-6975](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6975)
  - fix geo location campaigns (MNY)
    [MNY-7016](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7016)
  - mock prototype (Pro.js)
    [PFE-3815](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3815)
  - mock test (Pro.js)
    [PFE-3815](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3815)
  - mock testAPI (Pro.js)
    [PFE-3815](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3815)
  - add api key support to authentication manager (Pro.js)
    [PFE-6586](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6586)
  - pro console error while building pro rollup js (Pro.js)
    [PFE-6893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6893)
  - use aggrid modules to reduce bundle size (Pro.js)
    [PRO-6131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6131)
  - bad request message in the news widget (Pro.js)
    [PRO-6485](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6485)
  - the placement of columns aren t being saved to the layout and tend to reset when switching (Pro.js)
    [PRO-6554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6554)
  - ag grid update the license key and bump up version to 28 1 (Pro.js)
    [PRO-6936](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6936)
  - add the watchlist selection menu for the watchlist widget when in flight mode (Pro.js)
    [PRO-7119](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7119)
  - migrate upsellpricing to benzinga user conntext (Pro.js)
    [PRO-7206](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7206)
  - fix search analytics pro (Pro.js)
    [Pro-5938](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5938)
  - clean up widget toolbar (Pro.js)
    [Pro-6666](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6666)
  - update ci so that it does not build when there is no code change (Pro.js)
    [Pro-6674](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6674)
  - change pro ci to benzinga com (Pro.js)
    [Pro-6722](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6722)
  - add validation logic for the copy and paste functionality of the news widget for users with a (Pro.js)
    [Pro-6850](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6850)
  - optimize pro (Pro.js)
    [Pro-6894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6894)
  - removing settimeout (Pro.js)
    [Pro-6913](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6913)
  - use user settings to store scans (Pro.js)
    [Pro-7042](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7042)
  - mobile app release 3 4 3 (BZM)
    [BZM-7444](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7444)
  - mark notification as read when user opens app from it (BZM)
    [BZM-7565](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7565)
  - tweet spacing issue (BZM)
    [BZM-7586](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7586)
  - hide notification error alert (BZM)
    [BZM-7591](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7591)
  - improve realm labels and add channel info (BZM)
    [BZM-7595](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7595)
  - upgrade webrtc library (BZM)
    [BZM-7624](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7624)
  - migrate all news apis to news manager in mobile (BZM)
    [BZM-7764](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7764)
  - integrate notification config apis to data manager (BZM)
    [BZM-8268](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8268)
  - resolve colorpalette compile issue on master (BZM)
    [BZM-8984](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8984)
  - unique sandbox url next money (BZNext.js)
    [BZNEXT-313](https://gitlab.benzinga.io/benzinga/fusion/-/issues/313)
  - migrate articledata to internalnode type (BZNext.js)
    [BZNEXT-5130](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5130)
  - remove redux from bz next (BZNext.js)
    [BZNEXT-5539](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5539)
  - create react component commodities global indices (BZNext.js)
    [BZNEXT-5719](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5719)
  - sandbox eks (BZNext.js)
    [BZNEXT-599](https://gitlab.benzinga.io/benzinga/fusion/-/issues/599)
  - optimize analyst ratings ssr data (BZNext.js)
    [BZNEXT-6104](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6104)
  - improve revalidate time for amp pages (BZNext.js)
    [BZNEXT-6397](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6397)
  - acf featured products (BZNext.js)
    [BZNEXT-6439](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6439)
  - diagnose large unused chunks of press release page (BZNext.js)
    [BZNEXT-6791](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6791)
  - migrate bars to data package (BZNext.js)
    [BZNEXT-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - migrate data to be merged to data package part of maintenance week (BZNext.js)
    [BZNEXT-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - remove autocomplete from data to be merged (BZNext.js)
    [BZNEXT-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - remove unused data to be merged libraries (BZNext.js)
    [BZNEXT-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - analyst ratings analyst page (BZNext.js)
    [BZNEXT-6864](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6864)
  - remove unneeded javascript antd jsx icons uipopper (BZNext.js)
    [BZNEXT-6886](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6886)
  - forex profit calculator (BZNext.js)
    [BZNEXT-6946](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6946)
  - send user properties dimensions to ga4 (BZNext.js)
    [BZNEXT-6958](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6958)
  - develop compact crypto quote card (BZNext.js)
    [BZNEXT-6989](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6989)
  - removed deprecated components part of maintenance week (BZNext.js)
    [BZNEXT-7068](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7068)
  - debug parsely heartbeat time on page (BZNext.js)
    [BZNEXT-7151](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7151)
  - cleanup code a follow up from resolve fixes to canonical url redirect (BZNext.js)
    [BZNEXT-7158](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7158)
  - improve nft section on homepage (BZNext.js)
    [BZNEXT-7162](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7162)
  - design query parameter filtering passthrough to apis frontend filters (BZNext.js)
    [BZNEXT-7171](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7171)
  - add author content type to search index (BZNext.js)
    [BZNEXT-7182](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7182)
  - redo implement frontend interface for self checkout (BZNext.js)
    [BZNEXT-7210](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7210)
  - mergers acquisitions name update handle new ticker routing (BZNext.js)
    [BZNEXT-7228](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7228)
  - update premium button (BZNext.js)
    [BZNEXT-7253](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7253)
  - implement smartasset captivate tools across desktop inventory (BZNext.js)
    [BZNEXT-7269](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7269)
  - add body text matching to search results (BZNext.js)
    [BZNEXT-7270](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7270)
  - exclusives are not sorted correctly (BZNext.js)
    [BZNEXT-7273](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7273)
  - add how to invest in jewelry to menu (BZNext.js)
    [BZNEXT-7285](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7285)
  - update bz login page (BZNext.js)
    [BZNEXT-7286](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7286)
  - convey analyst metrics (BZNext.js)
    [BZNEXT-7287](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7287)
  - update tier details for api self checkout (BZNext.js)
    [BZNEXT-7289](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7289)
  - update the property used for exchange (BZNext.js)
    [BZNEXT-7300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7300)
  - add custom action for navigation menu clicks (BZNext.js)
    [BZNEXT-7302](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7302)
  - fixes to search results news query api (BZNext.js)
    [BZNEXT-7307](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7307)
  - bug fixes minor ui ux improvements (BZNext.js)
    [BZNEXT-7320](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7320)
  - task bug fixes minor ui ux improvements (BZNext.js)
    [BZNEXT-7320](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7320)
  - cboe positive keyword targeting discussion and suggestions for implementation (BZNext.js)
    [BZNEXT-8384](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8384)
  - updates setup alts style landing page for mortgage lenders and others (BZNext.js)
    [BZNEXT-7326](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7326)
  - updated menu navigation links (BZNext.js)
    [BZNEXT-7329](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7329)
  - redo modify compare product table w promo feature option and add attributes to mobile view (BZNext.js)
    [BZNEXT-7332](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7332)
  - update to premium page (BZNext.js)
    [BZNEXT-7333](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7333)
  - add tag for alternative investment product offering landing pages (BZNext.js)
    [BZNEXT-7335](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7335)
  - filtering news by insidertrades is pulling old results dotcom (BZNext.js)
    [BZNEXT-7343](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7343)
  - alts individual offering alternative asset page (BZNext.js)
    [BZNEXT-7346](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7346)
  - remove cision block on homepage (BZNext.js)
    [BZNEXT-7348](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7348)
  - alt hub pages similar offering card mimics individual offering (BZNext.js)
    [BZNEXT-7349](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7349)
  - follow on google news article button 50 sov (BZNext.js)
    [BZNEXT-7350](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7350)
  - sponsored content articles included in quote profile news feed (BZNext.js)
    [BZNEXT-7351](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7351)
  - full navigation bar sticks w scroll (BZNext.js)
    [BZNEXT-7356](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7356)
  - fda calendar missing logo crash (BZNext.js)
    [BZNEXT-7361](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7361)
  - notification manager (BZNext.js)
    [BZNEXT-7375](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7375)
  - issue with article on mobile (BZNext.js)
    [BZNEXT-7383](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7383)
  - taboola tag ready for implementation (BZNext.js)
    [BZNEXT-7405](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7405)
  - forex profit calculator (BZNext.js)
    [BZNEXT-6946](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6946)
  - fix nfts button and link to nft page (BZNext.js)
    [BZNEXT-7412](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7412)
  - faq updates (BZNext.js)
    [BZNEXT-7418](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7418)
  - remove them from the latest news feed and place 1 of the latest sponsored posts at the bottom (BZNext.js)
    [BZNEXT-7425](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7425)
  - amazon content below politics content (BZNext.js)
    [BZNEXT-7433](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7433)
  - remove realuuid if not provided (BZNext.js)
    [BZNEXT-7436](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7436)
  - add edit new cms button on article page (BZNext.js)
    [BZNEXT-7438](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7438)
  - headline only fixes landing page for pro (BZNext.js)
    [BZNEXT-7443](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7443)
  - fixed ad placement (Pro.js)
    [PRO-7297](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7297)
  - update covey template to allow for text (BZNext.js)
    [BZNEXT-7449](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7449)
  - alts fixes (BZNext.js)
    [BZNEXT-7452](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7452)
  - new ic placements w tags (BZNext.js)
    [BZNEXT-7459](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7459)
  - analyst profile page next steps (BZNext.js)
    [BZNEXT-7460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7460)
  - handle missing list html on product description (BZNext.js)
    [BZNEXT-7461](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7461)
  - leadgen opportunities are squished can t see details (BZNext.js)
    [BZNEXT-7466](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7466)
  - bring back cision block (BZNext.js)
    [BZNEXT-7468](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7468)
  - add boost to benzinga header (BZNext.js)
    [BZNEXT-7469](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7469)
  - style issues on dot com (BZNext.js)
    [BZNEXT-7472](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7472)
  - more cta buttons on alt platform asset pages (BZNext.js)
    [BZNEXT-7475](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7475)
  - ticker hover css issue (BZNext.js)
    [BZNEXT-7485](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7485)
  - add monetization bubble in the left handrail (BZNext.js)
    [BZNEXT-7497](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7497)
  - add covey link to ideas tab add another best to make it even (BZNext.js)
    [BZNEXT-7503](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7503)
  - missing analyst ratings in block bug (BZNext.js)
    [BZNEXT-7509](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7509)
  - navigation layout issue (BZNext.js)
    [BZNEXT-7514](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7514)
  - promote pro realtime quotes on quote page (BZNext.js)
    [BZNEXT-7522](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7522)
  - republish button in cms (BZNext.js)
    [BZNEXT-7523](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7523)
  - update sponsored partner content display logic (BZNext.js)
    [BZNEXT-7539](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7539)
  - microsoft login button (BZNext.js)
    [BZNEXT-7543](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7543)
  - yield page fixes (BZNext.js)
    [BZNEXT-7544](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7544)
  - touch up chart widgets (BZNext.js)
    [BZNEXT-7546](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7546)
  - update pre market link in navigation (BZNext.js)
    [BZNEXT-7554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7554)
  - add links to name of investment (BZNext.js)
    [BZNEXT-7560](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7560)
  - swith back to normal learderboard from sticky banner (BZNext.js)
    [BZNEXT-7562](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7562)
  - black friday mega banner (BZNext.js)
    [BZNEXT-7578](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7578)
  - fix image size issue w image gallery (BZNext.js)
    [BZNEXT-7582](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7582)
  - sticky bottom nav bar (BZNext.js)
    [BZNEXT-7583](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7583)
  - add additional configuration boxes to chart widget configurator (BZNext.js)
    [BZNEXT-7589](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7589)
  - alphastream updates (BZNext.js)
    [BZNEXT-7602](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7602)
  - develop money header components (BZNext.js)
    [BZNEXT-7603](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7603)
  - develop movers top stocks additional content section (BZNext.js)
    [BZNEXT-7604](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7604)
  - develop reviews section sponsored content (BZNext.js)
    [BZNEXT-7605](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7605)
  - header ad unit at top of page above drop down bars (BZNext.js)
    [BZNEXT-7607](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7607)
  - style fixes to ad header unit (BZNext.js)
    [BZNEXT-7621](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7621)
  - configuring a wistia player block autoplaying video content in our articles (BZNext.js)
    [BZNEXT-7628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7628)
  - disable campaignify units for newsbreak (BZNext.js)
    [BZNEXT-7630](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7630)
  - add forex profit calculator to sub navbar (BZNext.js)
    [BZNEXT-7631](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7631)
  - partner hub page integration aka bitcoin ira (BZNext.js)
    [BZNEXT-7632](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7632)
  - newsfeed widget (BZNext.js)
    [BZNEXT-7638](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7638)
  - ad header size change (BZNext.js)
    [BZNEXT-7639](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7639)
  - revert env var update (BZNext.js)
    [BZNEXT-7641](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7641)
  - dropdowns not responsive (BZNext.js)
    [BZNEXT-7643](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7643)
  - analyze popular stories request (BZNext.js)
    [BZNEXT-7651](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7651)
  - missing recent stories (BZNext.js)
    [BZNEXT-7652](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7652)
  - down markets widget display remove tickers (BZNext.js)
    [BZNEXT-7666](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7666)
  - gold ira monetization widget (BZNext.js)
    [BZNEXT-7667](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7667)
  - ignore api routes in redirect middleware (BZNext.js)
    [BZNEXT-7673](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7673)
  - touchup landing page (BZNext.js)
    [BZNEXT-7674](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7674)
  - render widgets (BZNext.js)
    [BZNEXT-7675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7675)
  - remove initial black friday article (BZNext.js)
    [BZNEXT-7679](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7679)
  - update the security name to heritage global inc on com (BZNext.js)
    [BZNEXT-7691](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7691)
  - follow up from resolve fix search bar aseventcapture (BZNext.js)
    [BZNEXT-7701](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7701)
  - watchlist widgets reformatting spacing fixes on landing page (BZNext.js)
    [BZNEXT-7704](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7704)
  - fix layout issue w cta in sidebar (BZNext.js)
    [BZNEXT-7707](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7707)
  - fix crypto page fundamentals css issue (BZNext.js)
    [BZNEXT-7712](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7712)
  - improve newsbreak referral check for com (BZNext.js)
    [BZNEXT-7723](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7723)
  - covey ideas tab mobile view (BZNext.js)
    [BZNEXT-7724](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7724)
  - product card badge bug (BZNext.js)
    [BZNEXT-7728](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7728)
  - add cms tag to distinguish and track drupal wordpress traffic (BZNext.js)
    [BZNEXT-7736](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7736)
  - adhc quote page error (BZNext.js)
    [BZNEXT-7737](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7737)
  - add secuirty txt file to com (BZNext.js)
    [BZNEXT-7738](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7738)
  - cleanup ad ux (BZNext.js)
    [BZNEXT-7739](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7739)
  - remove crypto event from menu (BZNext.js)
    [BZNEXT-7740](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7740)
  - optimized article page layout proposal (BZNext.js)
    [BZNEXT-7749](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7749)
  - cleanup site experience for newsbreak referrals (BZNext.js)
    [BZNEXT-7756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7756)
  - cleanup site experience for newsbreak referrals 2 (BZNext.js)
    [BZNEXT-7756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7756)
  - cleanup site experience for newsbreak referrals 3 (BZNext.js)
    [BZNEXT-7756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7756)
  - follow up from resolve covey ideas tab mobile view (BZNext.js)
    [BZNEXT-7759](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7759)
  - fix login redirection on com (BZNext.js)
    [BZNEXT-7761](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7761)
  - quote naming switch (BZNext.js)
    [BZNEXT-7762](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7762)
  - updates to about me (BZNext.js)
    [BZNEXT-7768](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7768)
  - update article ad layout push campaignify (BZNext.js)
    [BZNEXT-7771](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7771)
  - yield page improvements (BZNext.js)
    [BZNEXT-7773](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7773)
  - home page tabs snip of readable text vs fully expanded text (BZNext.js)
    [BZNEXT-7776](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7776)
  - follow up from analyst ratings table analyst styling issues (BZNext.js)
    [BZNEXT-7790](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7790)
  - wrap profile components with error boundry (BZNext.js)
    [BZNEXT-7809](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7809)
  - briefs right rail adjustment (BZNext.js)
    [BZNEXT-7810](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7810)
  - nye benzinga logo (BZNext.js)
    [BZNEXT-7812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7812)
  - move widgets pages to proto (BZNext.js)
    [BZNEXT-7814](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7814)
  - fix widget landing page mobile ui (BZNext.js)
    [BZNEXT-7820](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7820)
  - back to normal bz logo (BZNext.js)
    [BZNEXT-7823](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7823)
  - decimal formatting for yield investments (BZNext.js)
    [BZNEXT-7825](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7825)
  - improved article ux ad layout changes (BZNext.js)
    [BZNEXT-7826](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7826)
  - bug w sanitized html render (BZNext.js)
    [BZNEXT-7831](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7831)
  - remove photon from gitlab ci yml (BZNext.js)
    [BZNEXT-7848](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7848)
  - calculate rating summaries using appropriate date field (BZNext.js)
    [BZNEXT-7861](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7861)
  - updates to alt offerings product card (BZNext.js)
    [BZNEXT-7877](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7877)
  - mlk logo setup date based logo display (BZNext.js)
    [BZNEXT-7881](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7881)
  - update com to remove any use of imagecache and use the original image path (BZNext.js)
    [BZNEXT-7882](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7882)
  - yield page checkbox filter for risk (BZNext.js)
    [BZNEXT-7883](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7883)
  - homepage cms template (BZNext.js)
    [BZNEXT-7890](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7890)
  - create yield page embeddable widget (BZNext.js)
    [BZNEXT-7892](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7892)
  - pagespeed break down libs ui into smaller more optimized bundles (BZNext.js)
    [BZNEXT-7893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7893)
  - homepage lighthouse seo optimizations (BZNext.js)
    [BZNEXT-7894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7894)
  - remove console logs (BZNext.js)
    [BZNEXT-7895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7895)
  - next improve referral utm tracking for click scroll through pageviews (BZNext.js)
    [BZNEXT-7898](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7898)
  - homepage lighthouse accessibility optimizations (BZNext.js)
    [BZNEXT-7899](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7899)
  - gold widget integration address integration feedback (BZNext.js)
    [BZNEXT-7900](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7900)
  - update benzinga mobile logo (BZNext.js)
    [BZNEXT-7906](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7906)
  - slow video player load times on new article layout (BZNext.js)
    [BZNEXT-7921](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7921)
  - alternative assets offerings screener improvements (BZNext.js)
    [BZNEXT-7923](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7923)
  - widget landing page redesign calculator addition newsfeed redesign fix chart widget footer (BZNext.js)
    [BZNEXT-7928](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7928)
  - rename some news components (BZNext.js)
    [BZNEXT-7929](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7929)
  - yield widget on https www benzinga com yield to load below article if channel is bonds (BZNext.js)
    [BZNEXT-7932](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7932)
  - setup super bowl sunday logo website mobile (BZNext.js)
    [BZNEXT-7937](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7937)
  - remove retail sales calendar from com tools navigation (BZNext.js)
    [BZNEXT-7954](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7954)
  - fix firefox header on bz india site (BZNext.js)
    [BZNEXT-7959](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7959)
  - create calendar widget (BZNext.js)
    [BZNEXT-7967](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7967)
  - add height width and resize to widget builder (BZNext.js)
    [BZNEXT-7971](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7971)
  - debug ui issue on arrived homes profile (BZNext.js)
    [BZNEXT-7980](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7980)
  - add plus token to cookie cleaner (BZNext.js)
    [BZNEXT-7997](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7997)
  - clean ups for lighthouse optimizations (BZNext.js)
    [BZNEXT-7999](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7999)
  - bz india social media links need to be updated to ones for benzinga india specific open in new (BZNext.js)
    [BZNEXT-8002](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8002)
  - bz india ability to pin top 3 stories if we can t do the big display cards for now just making (BZNext.js)
    [BZNEXT-8005](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8005)
  - ad targeting by ticker (BZNext.js)
    [BZNEXT-8020](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8020)
  - bz india use ist timezone on article page (BZNext.js)
    [BZNEXT-8022](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8022)
  - setup valentine s day logo website mobile (BZNext.js)
    [BZNEXT-8024](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8024)
  - add a nav bar icon with invest in art between tv and premium for masterworks (BZNext.js)
    [BZNEXT-8026](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8026)
  - extra data params for benzinga widgets (BZNext.js)
    [BZNEXT-8027](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8027)
  - advertise with us issues move page react (BZNext.js)
    [BZNEXT-8031](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8031)
  - make disclosure block cleanup featured products block (BZNext.js)
    [BZNEXT-8034](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8034)
  - campaign disclosure not working displaying (BZNext.js)
    [BZNEXT-8037](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8037)
  - add impression pixel to parnter widgte (BZNext.js)
    [BZNEXT-8040](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8040)
  - yield page seo improvements (BZNext.js)
    [BZNEXT-8050](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8050)
  - add relevant recent upcoming events to quote pages (BZNext.js)
    [BZNEXT-8051](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8051)
  - top stories ticker removal right rail (BZNext.js)
    [BZNEXT-8053](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8053)
  - yield mobile layout (BZNext.js)
    [BZNEXT-8060](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8060)
  - load trending articles in infinite mode (BZNext.js)
    [BZNEXT-8068](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8068)
  - edit affiliate link in footer (BZNext.js)
    [BZNEXT-8070](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8070)
  - revert affiliate link (BZNext.js)
    [BZNEXT-8072](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8072)
  - fix chart and newsfeed embed code (BZNext.js)
    [BZNEXT-8077](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8077)
  - add header preload for primis script (BZNext.js)
    [BZNEXT-8079](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8079)
  - bz india seo meta canonical and jsongraph update (BZNext.js)
    [BZNEXT-8081](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8081)
  - make all news on home page act like a tab rather than a link (BZNext.js)
    [BZNEXT-8086](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8086)
  - fixes to stockstable migrate to fusion (BZNext.js)
    [BZNEXT-8098](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8098)
  - change premium button to research on com menu (BZNext.js)
    [BZNEXT-8103](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8103)
  - migrate author pages to next add follow button to article (BZNext.js)
    [BZNEXT-8105](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8105)
  - updates to invest in art page (BZNext.js)
    [BZNEXT-8106](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8106)
  - contributor content feed widget (BZNext.js)
    [BZNEXT-8108](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8108)
  - public com charts quote page integration (BZNext.js)
    [BZNEXT-8114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8114)
  - migrate recent briefs exclusives pages to cms (BZNext.js)
    [BZNEXT-8115](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8115)
  - public sponsoring https www benzinga com yield (BZNext.js)
    [BZNEXT-8122](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8122)
  - 50 50 ad unit campaign layout w b2c impression collection (BZNext.js)
    [BZNEXT-8126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8126)
  - 50 50 ad unit campaign layout w b2c impression collection improessions (BZNext.js)
    [BZNEXT-8126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8126)
  - rocket mortgage negative keyword targeting (BZNext.js)
    [BZNEXT-8127](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8127)
  - incorrect calculation for super bowl sunday logo (BZNext.js)
    [BZNEXT-8128](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8128)
  - showing up as amp on bz india website (BZNext.js)
    [BZNEXT-8134](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8134)
  - update quote charts w public supported tickers json (BZNext.js)
    [BZNEXT-8160](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8160)
  - improve caching policy experience for com pages (BZNext.js)
    [BZNEXT-8178](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8178)
  - widgets cleanups (BZNext.js)
    [BZNEXT-8187](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8187)
  - update partner profile page (BZNext.js)
    [BZNEXT-8192](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8192)
  - us flag toggles for in benzinga com articles (BZNext.js)
    [BZNEXT-8193](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8193)
  - follow up from resolve calculate rating summaries using appropriate date field (BZNext.js)
    [BZNEXT-8194](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8194)
  - load initial news server side on quote page news tab (BZNext.js)
    [BZNEXT-8197](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8197)
  - cleanup content feed links layout and analytics (BZNext.js)
    [BZNEXT-8199](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8199)
  - update exchange display (BZNext.js)
    [BZNEXT-8200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8200)
  - change invest in art button to art investing and link to external page (BZNext.js)
    [BZNEXT-8207](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8207)
  - add embed icon to content feeds to share buttons (BZNext.js)
    [BZNEXT-8209](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8209)
  - add edit page button to money post (BZNext.js)
    [BZNEXT-8210](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8210)
  - quick links fixes to india site (BZNext.js)
    [BZNEXT-8222](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8222)
  - add userid as a param to bz news api (BZNext.js)
    [BZNEXT-8224](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8224)
  - revert login iframe (BZNext.js)
    [BZNEXT-8234](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8234)
  - 100 new ad layout (BZNext.js)
    [BZNEXT-8237](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8237)
  - updates to stock relevant recent event messages (BZNext.js)
    [BZNEXT-8246](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8246)
  - terms not loading in posted in section (BZNext.js)
    [BZNEXT-8249](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8249)
  - debug date issue on google (BZNext.js)
    [BZNEXT-8254](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8254)
  - new ad campaign layout for amp (BZNext.js)
    [BZNEXT-8258](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8258)
  - create v2 chart widget w tickerbox (BZNext.js)
    [BZNEXT-8267](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8267)
  - skimlinks javascript install on press releases (BZNext.js)
    [BZNEXT-8273](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8273)
  - improve crypto quote page screen (BZNext.js)
    [BZNEXT-8274](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8274)
  - disable certain quote values on com for now (BZNext.js)
    [BZNEXT-8282](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8282)
  - add ticker to surrogate key for quote pages (BZNext.js)
    [BZNEXT-8291](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8291)
  - setup women s day logo website mobile (BZNext.js)
    [BZNEXT-8301](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8301)
  - apple news feed update (BZNext.js)
    [BZNEXT-8307](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8307)
  - improve default cache time for quote pages (BZNext.js)
    [BZNEXT-8308](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8308)
  - on the website footer tools features news widget leads to a 404 page (BZNext.js)
    [BZNEXT-8322](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8322)
  - fix redirect url on login page after authentication (BZNext.js)
    [BZNEXT-8323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8323)
  - update article draft loading screens ui (BZNext.js)
    [BZNEXT-8332](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8332)
  - alter article infinite scroll to show partial view (BZNext.js)
    [BZNEXT-8334](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8334)
  - setup st patty s day friday march 17th logo website mobile (BZNext.js)
    [BZNEXT-8338](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8338)
  - swap benzinga plus to benzinga research (BZNext.js)
    [BZNEXT-8339](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8339)
  - fix missing admin editor (BZNext.js)
    [BZNEXT-8344](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8344)
  - analytics calendar should use link for analyst names to support good seo practices (BZNext.js)
    [BZNEXT-8345](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8345)
  - infinite scroll analytics bug (BZNext.js)
    [BZNEXT-8346](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8346)
  - limit campaignify on articles for newsbreak referral (BZNext.js)
    [BZNEXT-8352](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8352)
  - limit campaignify on articles for newsbreak referral 2 (BZNext.js)
    [BZNEXT-8352](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8352)
  - limit campaignify on articles for newsbreak referral 3 (BZNext.js)
    [BZNEXT-8352](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8352)
  - better spacing for money investing page (BZNext.js)
    [BZNEXT-8353](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8353)
  - redirect to value of next param for both social logins and username password login (BZNext.js)
    [BZNEXT-8359](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8359)
  - remove getpremiumarticles from api sponsored and use the method in the basic news manager (BZNext.js)
    [BZNEXT-8370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8370)
  - cboe positive keyword targeting discussion and suggestions for implementation (BZNext.js)
    [BZNEXT-8384](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8384)
  - remove india content from amp pages (BZNext.js)
    [BZNEXT-8385](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8385)
  - fix missing sponsored content reach content type (BZNext.js)
    [BZNEXT-8389](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8389)
  - resolve infinite refresh bug (BZNext.js)
    [BZNEXT-8394](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8394)
  - rank for dividends today (BZNext.js)
    [BZNEXT-8395](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8395)
  - add refresh statuscode check before logging out (BZNext.js)
    [BZNEXT-8405](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8405)
  - cache services taxonomy endpoints (BZNext.js)
    [BZNEXT-8409](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8409)
  - fix quotes page news to also load benzinga reach content (BZNext.js)
    [BZNEXT-8438](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8438)
  - revert show only google news button on article pages (BZNext.js)
    [BZNEXT-8443](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8443)
  - fix build next builds (BZNext.js)
    [BZNEXT-8447](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8447)
  - reactdom render is no longer supported in react 18 (BZNext.js)
    [BZNEXT-8450](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8450)
  - implementing taboola placements tags attached 3 (BZNext.js)
    [BZNEXT-8453](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8453)
  - make expert ideas box scrollable (BZNext.js)
    [BZNEXT-8467](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8467)
  - fix mobile header css issues (BZNext.js)
    [BZNEXT-8468](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8468)
  - fix missing module node html parser (BZNext.js)
    [BZNEXT-8471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8471)
  - rollback react dom update (BZNext.js)
    [BZNEXT-8478](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8478)
  - revert of rollback react dom update changes (BZNext.js)
    [BZNEXT-8480](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8480)
  - fix calendar issue (BZNext.js)
    [BZNEXT-8484](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8484)
  - setup easter logo website mobile (BZNext.js)
    [BZNEXT-8487](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8487)
  - fix missing analytics on quotes pages (BZNext.js)
    [BZNEXT-8492](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8492)
  - update dfp tag name (BZNext.js)
    [BZNEXT-8499](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8499)
  - add to watchlist broken on quotes pages at least (BZNext.js)
    [BZNEXT-8501](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8501)
  - add new style option to news subscription box (BZNext.js)
    [BZNEXT-8511](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8511)
  - tradestation positive keyword targeting (BZNext.js)
    [BZNEXT-8525](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8525)
  - resolve bz widget class being removed from widgets in editorial cms (BZNext.js)
    [BZNEXT-8526](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8526)
  - change dynamic to react lazy (BZNext.js)
    [BZNEXT-8528](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8528)
  - add free newsletters button under tools trade ideas (BZNext.js)
    [BZNEXT-8540](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8540)
  - fix auth iframe support (BZNext.js)
    [BZNEXT-8542](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8542)
  - redesign money page (BZNext.js)
    [BZNEXT-8544](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8544)
  - add spikes volatility (BZNext.js)
    [BZNEXT-8547](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8547)
  - mobile login styling touchups (BZNext.js)
    [BZNEXT-8552](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8552)
  - change relative urls to absolute in menu api (BZNext.js)
    [BZNEXT-8553](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8553)
  - all ratings 1000 are shown even though the date selected is current after we toggle between (BZNext.js)
    [BZNEXT-8556](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8556)
  - more hydration issues (BZNext.js)
    [BZNEXT-8563](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8563)
  - turn top campaignify placement on for all article pages (BZNext.js)
    [BZNEXT-8564](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8564)
  - migrate premarket page to fusion (BZNext.js)
    [BZNEXT-8577](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8577)
  - need to change amp tag for primis player (BZNext.js)
    [BZNEXT-8581](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8581)
  - quote page tab undefined bug (BZNext.js)
    [BZNEXT-8582](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8582)
  - add apple association files (BZNext.js)
    [BZNEXT-8585](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8585)
  - setup earth day sat apr 22 2023 logo website mobile (BZNext.js)
    [BZNEXT-8587](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8587)
  - setup star wars day thurs may 04 2023 logo website mobile (BZNext.js)
    [BZNEXT-8588](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8588)
  - follow up on add spikes volatility (BZNext.js)
    [BZNEXT-8593](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8593)
  - fix short interest page 5xx error (BZNext.js)
    [BZNEXT-8598](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8598)
  - follow up from resolve ticker short interest error (BZNext.js)
    [BZNEXT-8618](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8618)
  - prevent duplicate article rendering (BZNext.js)
    [BZNEXT-8620](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8620)
  - collapsed infinite scroll changes (BZNext.js)
    [BZNEXT-8622](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8622)
  - new settings page (BZNext.js)
    [BZNEXT-8627](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8627)
  - buy now buttons on calendars (BZNext.js)
    [BZNEXT-8636](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8636)
  - add one in body google ad on benzinga india articles (BZNext.js)
    [BZNEXT-8639](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8639)
  - add additional errors to datadog rum ignore list (BZNext.js)
    [BZNEXT-8651](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8651)
  - setup logo for cinco de mayo on may 5th website mobile (BZNext.js)
    [BZNEXT-8653](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8653)
  - the navigation header and footer have responsive layout issues (BZNext.js)
    [BZNEXT-8657](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8657)
  - handle messaging for pro upsells at bottom of quote pr feed (BZNext.js)
    [BZNEXT-8668](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8668)
  - setup may 6 uk coronation day logo website mobile (BZNext.js)
    [BZNEXT-8672](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8672)
  - add google analytics 4 ga4 to amp pages (BZNext.js)
    [BZNEXT-8675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8675)
  - title tag addition for dividends today (BZNext.js)
    [BZNEXT-8677](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8677)
  - add advertise to the benzinga header (BZNext.js)
    [BZNEXT-8682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8682)
  - load analyst ratings data on server side (BZNext.js)
    [BZNEXT-8709](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8709)
  - add apple id login option to homepage login (BZNext.js)
    [BZNEXT-8712](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8712)
  - rephrase banners on quotes pages (BZNext.js)
    [BZNEXT-8713](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8713)
  - add nofollow noindex to sponsored source content (BZNext.js)
    [BZNEXT-8719](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8719)
  - next links broken (BZNext.js)
    [BZNEXT-8720](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8720)
  - resolve crypto page load error (BZNext.js)
    [BZNEXT-8752](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8752)
  - protocol undefined error (BZNext.js)
    [BZNEXT-8756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8756)
  - home page calendar please add company name to the calendar grid (BZNext.js)
    [BZNEXT-8757](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8757)
  - add the pro registration flow to the new login page so we can consolidate into one part one (BZNext.js)
    [BZNEXT-8759](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8759)
  - bug insider trades read properties of undefined (BZNext.js)
    [BZNEXT-8764](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8764)
  - bug cannot read properties of null postedin (BZNext.js)
    [BZNEXT-8765](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8765)
  - press releases indexing issue (BZNext.js)
    [BZNEXT-8770](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8770)
  - remove google one tap while broken (BZNext.js)
    [BZNEXT-8773](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8773)
  - incorrect canonical tag on template (BZNext.js)
    [BZNEXT-8778](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8778)
  - sidebar defaults (BZNext.js)
    [BZNEXT-8779](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8779)
  - update clear cache endpoint (BZNext.js)
    [BZNEXT-8782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8782)
  - fix one tap callback not firing (BZNext.js)
    [BZNEXT-8783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8783)
  - fix one tap callback not firing 2 (BZNext.js)
    [BZNEXT-8783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8783)
  - fix one tap callback not firing 3 (BZNext.js)
    [BZNEXT-8783](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8783)
  - change cta on real estate offering cards from get started to get offer (BZNext.js)
    [BZNEXT-8789](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8789)
  - setup logo for memorial day on may 29th website mobile (BZNext.js)
    [BZNEXT-8799](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8799)
  - press releases filter by content type not just entity (BZNext.js)
    [BZNEXT-8800](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8800)
  - remove user from search indexing (BZNext.js)
    [BZNEXT-8805](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8805)
  - update default image for chainwire stories (BZNext.js)
    [BZNEXT-8806](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8806)
  - remove smart asset unit from article infinite scroll (BZNext.js)
    [BZNEXT-8837](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8837)
  - add captcha to user registration and login needs details (BZNext.js)
    [BZNEXT-8850](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8850)
  - public please add to quotes buy button (BZNext.js)
    [BZNEXT-8852](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8852)
  - thumbnail images do not render on analyst stock ratings page (BZNext.js)
    [BZNEXT-8856](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8856)
  - fix custom server bz (BZNext.js)
    [BZNEXT-8857](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8857)
  - button block in headers do not populate (BZNext.js)
    [BZNEXT-8859](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8859)
  - hide platform details if empty (BZNext.js)
    [BZNEXT-8874](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8874)
  - taboola infinite load limited to 20 of articles (BZNext.js)
    [BZNEXT-8879](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8879)
  - fix iframe social auth (BZNext.js)
    [BZNEXT-8884](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8884)
  - new partnership with job board (BZNext.js)
    [BZNEXT-8892](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8892)
  - quote page sponsored content no algo just time based (BZNext.js)
    [BZNEXT-8900](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8900)
  - speed up news bar (BZNext.js)
    [BZNEXT-8905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8905)
  - amp pages produce 500 and should trigger 404s (BZNext.js)
    [BZNEXT-8908](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8908)
  - follow up from resolve fix every story that matters around the web widget not loading properly (BZNext.js)
    [BZNEXT-8913](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8913)
  - quote page insider trades seo improvements (BZNext.js)
    [BZNEXT-8918](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8918)
  - add author default sidebar (BZNext.js)
    [BZNEXT-8922](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8922)
  - make the images on the screener clickable go to affiliate link in offering sheet (BZNext.js)
    [BZNEXT-8924](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8924)
  - noindex secfilings after 90 days similar to the way it was done for press releases (BZNext.js)
    [BZNEXT-8925](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8925)
  - campaignify click out test (BZNext.js)
    [BZNEXT-8927](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8927)
  - add support for loginnext and registernext query params for sso (BZNext.js)
    [BZNEXT-8937](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8937)
  - fix login (BZNext.js)
    [BZNEXT-8945](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8945)
  - improved calendar buy button (BZNext.js)
    [BZNEXT-8947](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8947)
  - add cryptocurrency dfp tag (BZNext.js)
    [BZNEXT-8952](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8952)
  - fix sponsored content top stories sorting algo (BZNext.js)
    [BZNEXT-8958](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8958)
  - add public links to quote pages title desc (BZNext.js)
    [BZNEXT-8961](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8961)
  - patch infinite scroll (BZNext.js)
    [BZNEXT-8966](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8966)
  - make some ga anchor tag analytic events more specific (BZNext.js)
    [BZNEXT-8974](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8974)
  - no index old amp press releases (BZNext.js)
    [BZNEXT-8980](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8980)
  - handle content id for all content types (BZNext.js)
    [BZNEXT-8985](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8985)
  - add read more top stories button to articles fix length of sponsored content (BZNext.js)
    [BZNEXT-8989](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8989)
  - resolve some author pages 404 (BZNext.js)
    [BZNEXT-8994](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8994)
  - add z to robots txt disallow (BZNext.js)
    [BZNEXT-8998](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8998)
  - show primary image instead of primis video player for chainwire articles (BZNext.js)
    [BZNEXT-9001](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9001)
  - taboola ad deployment (BZNext.js)
    [BZNEXT-9005](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9005)
  - add main site link to login logged in (BZNext.js)
    [BZNEXT-9010](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9010)
  - filter story type on author pages (BZNext.js)
    [BZNEXT-9014](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9014)
  - noindex bzfeed pages (BZNext.js)
    [BZNEXT-9017](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9017)
  - ads need to be removed from sponsored content asap (BZNext.js)
    [BZNEXT-9023](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9023)
  - fix author page infinite scroll (BZNext.js)
    [BZNEXT-9024](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9024)
  - add utm source logging to hubspot for registrations (BZNext.js)
    [BZNEXT-9030](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9030)
  - redirect account to login if not logged in (BZNext.js)
    [BZNEXT-9031](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9031)
  - follow up from resolve ads need to be removed from sponsored content asap (BZNext.js)
    [BZNEXT-9033](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9033)
  - follow up from resolve resolve some author pages 404 (BZNext.js)
    [BZNEXT-9035](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9035)
  - fix author meta description (BZNext.js)
    [BZNEXT-9045](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9045)
  - remove google noindex tag from select pr feeds (BZNext.js)
    [BZNEXT-9046](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9046)
  - noindex benzinga partner content (BZNext.js)
    [BZNEXT-9047](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9047)
  - remove usage of enterprise ag grid on dot com (BZNext.js)
    [BZNEXT-9048](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9048)
  - account page touchup (BZNext.js)
    [BZNEXT-9049](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9049)
  - noindex search pages (BZNext.js)
    [BZNEXT-9051](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9051)
  - add register type for one tap (BZNext.js)
    [BZNEXT-9053](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9053)
  - fix register redirect (BZNext.js)
    [BZNEXT-9059](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9059)
  - noindex headline only posts (BZNext.js)
    [BZNEXT-9070](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9070)
  - taboola placement strategy for july (BZNext.js)
    [BZNEXT-9078](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9078)
  - remove the art investing button from navigation bar (BZNext.js)
    [BZNEXT-9082](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9082)
  - fix analyst ratings buy now (BZNext.js)
    [BZNEXT-9090](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9090)
  - missing canonical on login next (BZNext.js)
    [BZNEXT-9091](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9091)
  - change tag in new investments news feed query on alternative investments page from offering (BZNext.js)
    [BZNEXT-9093](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9093)
  - small style improvements to premarket page (BZNext.js)
    [BZNEXT-9115](https://gitlab.benzinga.io/benzinga/fusion/-/issues/9115)
  - epic branch (Data.js)
    [DATA-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - migrate money in data to be merged (Data.js)
    [DATA-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - migrate node in data to be merged (Data.js)
    [DATA-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - migrate quote client from data to be merged package (Data.js)
    [DATA-6838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6838)
  - allow newsdesk to posting to 1 or more chat channels (NewsdeskTools.js)
    [DESK-7821](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7821)
  - fix ci not building (FUSION)
    [FUSION-6749](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6749)
  - update the pfe sandbox auto deployment job criteria (fusion)
    [fusion-6995](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6995)
  - migrate chart widget into fusion (FUSION)
    [FUSION-7414](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7414)
  - V2 create ticker hover widget for external pages (FUSION)
    [FUSION-7426](https://gitlab.benzinga.io/benzinga/fusion/-/issues/7426)
  - clean up themetron (FUSION)
    [FUSION-8483](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8483)
  - fix issue where delayed quote type was wrong (FUSION)
    [FUSION-8934](https://gitlab.benzinga.io/benzinga/fusion/-/issues/8934)
  - change result to ok for safeawait (FUSSION)
    [FUSSION-6414](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6414)
  - home page calendar dividends tab ex dividend date (BZFE)
    [BZFE-4357](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4357)
  - home page add watch tab (BZFE)
    [BZFE-4478](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4478)
  - server side rendered calendar tables (BZFE)
    [BZFE-4940](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4940)
  - don t use https data api pro benzinga com for autocomplete in next com (BZFE)
    [BZFE-4956](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4956)
  - add ipos to calendar widget (BZFE)
    [BZFE-5148](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5148)
  - unverified traders ui (BZFE)
    [BZFE-5149](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5149)
  - migrate benzinga exclusives and subsequent tabs to fusion (BZFE)
    [BZFE-5162](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5162)
  - feature gated calendar register overlay (BZFE)
    [BZFE-5169](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5169)
  - login to benzinga website using google (BZFE)
    [BZFE-5287](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5287)
  - migrate keyword results page to fusion (BZFE)
    [BZFE-5288](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5288)
  - lazy load dynamic loading for in article calendar widget next (BZFE)
    [BZFE-5357](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5357)
  - set default canonical next 2 (BZFE)
    [BZFE-5421](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5421)
  - tapping on ios the close x for search input should not trigger the account menu next (BZFE)
    [BZFE-5460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5460)
  - improve scrolling for search on mobile next (BZFE)
    [BZFE-5461](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5461)
  - amp improvements next (BZFE)
    [BZFE-5473](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5473)
  - popup for global sites to users who are based in italy spain france (BZFE)
    [BZFE-5535](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5535)
  - follow up from task bzfe 5345 add image gallery block component (BZFE)
    [BZFE-5562](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5562)
  - remove sentry from next (BZFE)
    [BZFE-5656](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5656)
  - setup mobile vs desktop default ad banner sizes (BZFE)
    [BZFE-5713](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5713)
  - create react component for todays economic data (BZFE)
    [BZFE-5720](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5720)
  - we should default to last market day to get earnings (BZFE)
    [BZFE-5756](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5756)
  - crypto page w fundamentals (BZFE)
    [BZFE-5757](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5757)
  - add financialadvisorinformationcard component block (BZFE)
    [BZFE-5790](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5790)
  - migrate get quote block component scripts (BZFE)
    [BZFE-5799](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5799)
  - migrate get quote block component scripts 2 (BZFE)
    [BZFE-5799](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5799)
  - integrate full width header option (BZFE)
    [BZFE-5857](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5857)
  - migrate stock list component (BZFE)
    [BZFE-5861](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5861)
  - add get report on quote page ticker hover (BZFE)
    [BZFE-5876](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5876)
  - mortgage rates landing page (BZFE)
    [BZFE-5954](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5954)
  - investigate the quote page price timing bug (BZFE)
    [BZFE-5973](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5973)
  - add download report on ticker tile (BZFE)
    [BZFE-5981](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5981)
  - clean up social share (BZFE)
    [BZFE-5982](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5982)
  - add redirect option on submission to newsletter subscription box (BZFE)
    [BZFE-6018](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6018)
  - move money pages disclosure to footer (BZFE)
    [BZFE-6025](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6025)
  - hide date from main tiles (BZFE)
    [BZFE-6031](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6031)
  - keyword search encode url parameters (BZFE)
    [BZFE-6036](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6036)
  - fix logo layout on stock splits (BZFE)
    [BZFE-6037](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6037)
  - fix dividends sort order (BZFE)
    [BZFE-6038](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6038)
  - fix short interest data not loading (BZFE)
    [BZFE-6039](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6039)
  - fix author url meta next (BZFE)
    [BZFE-6052](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6052)
  - fix class undefined next (BZFE)
    [BZFE-6053](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6053)
  - integrate supported tickers for stock reports (BZFE)
    [BZFE-6071](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6071)
  - fix line clamp fallback image for exclusives carousel (BZFE)
    [BZFE-6092](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6092)
  - move internal content request to article manager wrap w safe promise (BZFE)
    [BZFE-6093](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6093)
  - #6094 BZFE RUNTIME_ENV should be available in next deployments (BZFE)
    [BZFE-6094](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6094)
  - layout fixes to floating cta (BZFE)
    [BZFE-6107](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6107)
  - premium product button placements (BZFE)
    [BZFE-6108](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6108)
  - use node env for segement (BZFE)
    [BZFE-6109](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6109)
  - cleanup click tracking (BZFE)
    [BZFE-6111](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6111)
  - typeerror cannot read property type of null (BZFE)
    [BZFE-6114](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6114)
  - implement priority offerings (BZFE)
    [BZFE-6120](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6120)
  - move content feed to news package (BZFE)
    [BZFE-6121](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6121)
  - update exclusive section link (BZFE)
    [BZFE-6124](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6124)
  - remove quotecontext from quotes page (BZFE)
    [BZFE-6126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6126)
  - fix ticker popup overlay issue (BZFE)
    [BZFE-6129](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6129)
  - ssr analyst rating summary table (BZFE)
    [BZFE-6139](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6139)
  - fix sort recommend (BZFE)
    [BZFE-6150](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6150)
  - fix exclusives stale while revalidate settings (BZFE)
    [BZFE-6151](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6151)
  - implement carousel at home page watch tab (BZFE)
    [BZFE-6153](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6153)
  - add neutral position option to trade idea post portal (BZFE)
    [BZFE-6154](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6154)
  - rotate read in app button w follow on google news (BZFE)
    [BZFE-6155](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6155)
  - click track cision pr block next (BZFE)
    [BZFE-6157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6157)
  - implement pro trade idea posting portal on com (BZFE)
    [BZFE-6162](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6162)
  - fix carousel mobile spacing (BZFE)
    [BZFE-6164](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6164)
  - fix event html entities limit to 1 line (BZFE)
    [BZFE-6165](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6165)
  - follow up from resolve implement pro trade idea posting portal on com (BZFE)
    [BZFE-6169](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6169)
  - moving up bullet points on desktop (BZFE)
    [BZFE-6184](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6184)
  - remove xlink href in favor of href in svgs (BZFE)
    [BZFE-6188](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6188)
  - update featuredarticles ui (BZFE)
    [BZFE-6189](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6189)
  - missing newsletter box (BZFE)
    [BZFE-6190](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6190)
  - fix css of native ic ads (BZFE)
    [BZFE-6196](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6196)
  - replace my watchlist news w nft news on crypto page (BZFE)
    [BZFE-6199](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6199)
  - follow up from resolve remove quotecontext from quotes page (BZFE)
    [BZFE-6203](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6203)
  - random fixes (BZFE)
    [BZFE-6205](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6205)
  - migrate content by state block (BZFE)
    [BZFE-6208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6208)
  - mortgage rates page fixes (BZFE)
    [BZFE-6209](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6209)
  - fix spacing issue on quotes page video fundamentals on mobile (BZFE)
    [BZFE-6210](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6210)
  - leverage caching for money content requests (BZFE)
    [BZFE-6213](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6213)
  - optimize crypto com units they kill pagespeed (BZFE)
    [BZFE-6219](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6219)
  - benzinga crypto logo (BZFE)
    [BZFE-6221](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6221)
  - Adds news archive sitemap to, removes old googlenews from robots.txt (BZFE)
    [BZFE-6225](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6225)
  - add mgid back to bottom of quote pages (BZFE)
    [BZFE-6235](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6235)
  - add alt tags to article primary images next (BZFE)
    [BZFE-6244](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6244)
  - BZFE GitLab CI uploads .next/static files to s3 (BZFE)
    [BZFE-6246](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6246)
  - develop landing pages for each commodity (BZFE)
    [BZFE-6248](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6248)
  - update benzinga crypto logo on crypto homepage and article pages (BZFE)
    [BZFE-6251](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6251)
  - mobile premium button to bz orange (BZFE)
    [BZFE-6259](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6259)
  - redirect middleware (BZFE)
    [BZFE-6264](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6264)
  - buy sell buttons on crypto quote pages (BZFE)
    [BZFE-6268](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6268)
  - 05 06 2022 benzinga com register account (BZFE)
    [BZFE-6271](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6271)
  - benzinga com remove ad from short articles (BZFE)
    [BZFE-6273](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6273)
  - mobile web story display improvements (BZFE)
    [BZFE-6276](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6276)
  - setup header prop to disable optinmonster script (BZFE)
    [BZFE-6279](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6279)
  - debug fatal crashes (BZFE)
    [BZFE-6281](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6281)
  - automated earnings recap fix fetched article timeframe (BZFE)
    [BZFE-6288](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6288)
  - remove the css files from bz next (BZFE)
    [BZFE-6312](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6312)
  - calculator image section is missing for some pages (BZFE)
    [BZFE-6319](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6319)
  - popup is not disappearing after tapping on it (BZFE)
    [BZFE-6320](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6320)
  - improvement clicking on the start investing button new page is opening in the same tab (BZFE)
    [BZFE-6321](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6321)
  - subscription success message discrepancy (BZFE)
    [BZFE-6322](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6322)
  - design layout is not available for table contents on several pages (BZFE)
    [BZFE-6323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6323)
  - font size is not same for headers in migrated page (BZFE)
    [BZFE-6324](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6324)
  - missing brokers table forex vs stocks (BZFE)
    [BZFE-6325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6325)
  - fixes to fda calendar estimated dates (BZFE)
    [BZFE-6334](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6334)
  - fix ticker popup add to watchlist alignment (BZFE)
    [BZFE-6337](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6337)
  - premium product block below articles (BZFE)
    [BZFE-6342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6342)
  - fix routing for premium product page (BZFE)
    [BZFE-6347](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6347)
  - capital rate calculator updates (BZFE)
    [BZFE-6350](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6350)
  - fix money client api (BZFE)
    [BZFE-6358](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6358)
  - fix analyst ratings component (BZFE)
    [BZFE-6359](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6359)
  - add product card variant detailed (BZFE)
    [BZFE-6362](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6362)
  - update bz slug route catch all sub paths (BZFE)
    [BZFE-6363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6363)
  - native ad slots not defined mobile article pages (BZFE)
    [BZFE-6365](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6365)
  - unusual options calendar fixes (BZFE)
    [BZFE-6367](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6367)
  - update home tabs (BZFE)
    [BZFE-6368](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6368)
  - something to make optinmonster not appear when logged in (BZFE)
    [BZFE-6384](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6384)
  - read in app on all mobile pageviews no more a b test (BZFE)
    [BZFE-6385](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6385)
  - analyst ratings page ranking for stock ratings (BZFE)
    [BZFE-6387](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6387)
  - remove benzinga insights whats going on from recent feed ss (BZFE)
    [BZFE-6388](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6388)
  - dynamic wp pages in next (BZFE)
    [BZFE-6396](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6396)
  - follow up from resolve premium product block below articles (BZFE)
    [BZFE-6401](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6401)
  - missing video for contents money arch roamright travel insurance review (BZFE)
    [BZFE-6402](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6402)
  - fix mobile tabs (BZFE)
    [BZFE-6403](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6403)
  - fda seo improvements (BZFE)
    [BZFE-6404](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6404)
  - google event for 404 error alerting (BZFE)
    [BZFE-6405](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6405)
  - trade ideas upvote downvote asset performance feedback (BZFE)
    [BZFE-6406](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6406)
  - cleanup benzinga crypto logo on mobile (BZFE)
    [BZFE-6407](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6407)
  - ignore news prefix for channel template w channel news (BZFE)
    [BZFE-6408](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6408)
  - image missing money cmc markets review (BZFE)
    [BZFE-6409](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6409)
  - load crypto price client side (BZFE)
    [BZFE-6410](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6410)
  - fix search dropdown overlay on quotes pages (BZFE)
    [BZFE-6412](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6412)
  - financial advisor firm fixes not a priority (BZFE)
    [BZFE-6418](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6418)
  - cls fix for crypto widget (BZFE)
    [BZFE-6423](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6423)
  - acf cryptocurrencies (BZFE)
    [BZFE-6433](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6433)
  - acf featured coins (BZFE)
    [BZFE-6434](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6434)
  - acf featured products (BZFE)
    [BZFE-6439](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6439)
  - acf workerscompdataform migrate (BZFE)
    [BZFE-6440](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6440)
  - news template sidebar (BZFE)
    [BZFE-6442](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6442)
  - update homepage trading school button (BZFE)
    [BZFE-6445](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6445)
  - acf how to buy (BZFE)
    [BZFE-6451](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6451)
  - follow up from resolve fixes to fda calendar estimated dates (BZFE)
    [BZFE-6454](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6454)
  - mobile ui ux updates (BZFE)
    [BZFE-6456](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6456)
  - hide premium product on articles with ad disclousure (BZFE)
    [BZFE-6460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6460)
  - acf products (BZFE)
    [BZFE-6466](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6466)
  - acf transparently integrate (BZFE)
    [BZFE-6467](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6467)
  - go to premium button style changes (BZFE)
    [BZFE-6470](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6470)
  - acf lemonade calculator widget (BZFE)
    [BZFE-6477](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6477)
  - fix premium product on article resizing (BZFE)
    [BZFE-6481](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6481)
  - add viewport for shorter screens 728x90 ad unit (BZFE)
    [BZFE-6484](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6484)
  - optimize bz js bundles (BZFE)
    [BZFE-6493](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6493)
  - connect futures currency ticker visibility data to frontend (BZFE)
    [BZFE-6501](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6501)
  - acf article groups (BZFE)
    [BZFE-6502](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6502)
  - cms migration code cleanup (BZFE)
    [BZFE-6504](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6504)
  - middle campaignify and center stage should stack (BZFE)
    [BZFE-6507](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6507)
  - analyst ratings calendar success score (BZFE)
    [BZFE-6510](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6510)
  - translation bug mobile menu (BZFE)
    [BZFE-6511](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6511)
  - hide read in app button on sponsored posts (BZFE)
    [BZFE-6512](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6512)
  - migrate zingrewards from data to be merged to it s own manager (Data.js)
    [DATA-5632](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5632)
  - change watchlist endpoint (Data.js)
    [Data-6167](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6167)
  - fix fusion linter (Fusion)
    [Fusion-6067](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6067)
  - update changelog system to use nx (Fusion)
    [Fusion-6430](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6430)
  - fix logo overlap issue (UI.js)
    [UI-4532](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4532)

# v2.10.0

## Feature
  - image component (BZNext.js)
    [BZFE-4594](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4594)
  - nft release calendar page (BZNext.js)
    [BZFE-4638](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4638)
  - sponsored content new article template (BZNext.js)
    [BZFE-4776](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4776)
  - nft product page (BZNext.js)
    [BZFE-4939](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4939)
  - alts landing page (BZNext.js)
    [BZFE-4946](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4946)
  - setup analyst rating calendars routes (BZNext.js)
    [BZFE-5061](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5061)
  - add login page (BZNext.js)
    [BZFE-5100](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5100)
  - alternative investments page (BZNext.js)
    [BZFE-5181](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5181)
  - see more on homepage news blocks (BZNext.js)
    [BZFE-5182](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5182)
  - setup main news listings layout (BZNext.js)
    [BZFE-5248](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5248)
  - Sets pretty URL as canonical for article page if available (BZNext.js)
    [BZFE-5467](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5467)
  - add iframe to homepage for products (BZNext.js)
    [BZFE-5760](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5760)
  - add art offerings hub page (BZNext.js)
    [BZFE-5970](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5970)
  - Migrate to Nx 13 (UI.js)
    [UI-4668](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4668)

## Bug
  - fix fusion next build error error when using sourcemap for reporting an error can t resolve (BZNext.js)
    [BZFE-4521](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4521)
  - fix key missing next (BZNext.js)
    [BZFE-4835](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4835)
  - fix forwardref issue next (BZNext.js)
    [BZFE-4836](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4836)
  - fix fetching of crypto price data in articles (BZNext.js)
    [BZFE-4902](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4902)
  - fix home page back from latest tab (BZNext.js)
    [BZFE-4909](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4909)
  - fix mobile width issue w anchor (BZNext.js)
    [BZFE-4926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4926)
  - query parameters not working in production (BZNext.js)
    [BZFE-4944](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4944)
  - fix ui storybook build error (BZNext.js)
    [BZFE-4957](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4957)
  - fix padding below primis unit (BZNext.js)
    [BZFE-4976](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4976)
  - broke footer on amp pages (BZNext.js)
    [BZFE-4979](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4979)
  - debug bounce rate issues (BZNext.js)
    [BZFE-4988](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4988)
  - fix linting error in ui money builds (BZNext.js)
    [BZFE-5021](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5021)
  - add styles for ul ol li and other common missing html tags in article page next (BZNext.js)
    [BZFE-5028](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5028)
  - fix amp article date timezone (BZNext.js)
    [BZFE-5055](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5055)
  - fda calendar fixes (BZNext.js)
    [BZFE-5062](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5062)
  - quote news tab keeping 2nd article mention (BZNext.js)
    [BZFE-5082](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5082)
  - fix scanner page filters (BZNext.js)
    [BZFE-5102](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5102)
  - search issue on benzinga for royal dutch shell (BZNext.js)
    [BZFE-5115](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5115)
  - hide article calendar widget when there no tickers (BZNext.js)
    [BZFE-5119](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5119)
  - fix media card crash (BZNext.js)
    [BZFE-5124](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5124)
  - fix alternative offerings overflow (BZNext.js)
    [BZFE-5150](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5150)
  - quote ratings tab is broken (BZNext.js)
    [BZFE-5152](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5152)
  - missing h tags on amp articles (BZNext.js)
    [BZFE-5155](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5155)
  - article 404 error (BZNext.js)
    [BZFE-5170](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5170)
  - analyst ratings page breaks if quotes error (BZNext.js)
    [BZFE-5174](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5174)
  - quote page exception (BZNext.js)
    [BZFE-5180](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5180)
  - analyst ratings bug fixes (BZNext.js)
    [BZFE-5183](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5183)
  - handle empty article body (BZNext.js)
    [BZFE-5198](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5198)
  - back navigation page jump bug (BZNext.js)
    [BZFE-5201](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5201)
  - typeerror moneyposts crashes homepage next (BZNext.js)
    [BZFE-5205](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5205)
  - quote page news block height bug (BZNext.js)
    [BZFE-5206](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5206)
  - ticker hover regex update (BZNext.js)
    [BZFE-5217](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5217)
  - remove duplicate parse ly meta (BZNext.js)
    [BZFE-5221](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5221)
  - filter missing bullet points (BZNext.js)
    [BZFE-5227](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5227)
  - fix article error add channel check (BZNext.js)
    [BZFE-5229](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5229)
  - alts mobile layout issue (BZNext.js)
    [BZFE-5236](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5236)
  - fix 0 on top stories block (BZNext.js)
    [BZFE-5239](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5239)
  - work on automated earnings faq phrase (BZNext.js)
    [BZFE-5283](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5283)
  - welcome create first watchlist bug fix (BZNext.js)
    [BZFE-5286](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5286)
  - import news bug (BZNext.js)
    [BZFE-5291](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5291)
  - ratings news bug fix (BZNext.js)
    [BZFE-5303](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5303)
  - ticker regex fix safari bug (BZNext.js)
    [BZFE-5304](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5304)
  - fix draft no image bug (BZNext.js)
    [BZFE-5305](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5305)
  - Fixes route for fonts, migrates to single asset directory (BZNext.js)
    [BZFE-5313](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5313)
  - fixes to alts layout on tablets (BZNext.js)
    [BZFE-5318](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5318)
  - home page mobile featured image layou (BZNext.js)
    [BZFE-5322](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5322)
  - fix missing image in draft crash (BZNext.js)
    [BZFE-5323](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5323)
  - fix layout sidebar shift (BZNext.js)
    [BZFE-5324](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5324)
  - margin calculator select ticker layout bug (BZNext.js)
    [BZFE-5338](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5338)
  - banner overlap issue (BZNext.js)
    [BZFE-5339](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5339)
  - fix amp loading bug (BZNext.js)
    [BZFE-5342](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5342)
  - cover extended case for ticker hover (BZNext.js)
    [BZFE-5353](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5353)
  - alt investments code cleanup min investment range fix (BZNext.js)
    [BZFE-5376](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5376)
  - amp page body errors for disallowed attribute or attribute value present in html tag (BZNext.js)
    [BZFE-5381](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5381)
  - amp page body errors for disallowed attribute or attribute value present in html tag 2 (BZNext.js)
    [BZFE-5381](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5381)
  - amp page body errors for disallowed attribute or attribute value present in html tag 3 (BZNext.js)
    [BZFE-5381](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5381)
  - amp page body errors for disallowed attribute or attribute value present in html tag 4 (BZNext.js)
    [BZFE-5381](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5381)
  - view draft article bug fixes (BZNext.js)
    [BZFE-5383](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5383)
  - quotes page q a dividned bug (BZNext.js)
    [BZFE-5395](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5395)
  - Fixes check for channels tags and drafts (BZNext.js)
    [BZFE-5404](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5404)
  - datepicker import fix (BZNext.js)
    [BZFE-5412](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5412)
  - fix meta alternative investments meta (BZNext.js)
    [BZFE-5418](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5418)
  - typeerror cannot read properties of null reading includes on quote pages next (BZNext.js)
    [BZFE-5423](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5423)
  - debug and fix undefined image issue next (BZNext.js)
    [BZFE-5424](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5424)
  - ticker box no longer inline on the sec page next (BZNext.js)
    [BZFE-5435](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5435)
  - ticker hover sending incorrect lookup next (BZNext.js)
    [BZFE-5459](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5459)
  - recent briefs page fails to load (BZNext.js)
    [BZFE-5500](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5500)
  - recent briefs page fails to load 2 (BZNext.js)
    [BZFE-5500](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5500)
  - fix amp article issue with share buttons (BZNext.js)
    [BZFE-5547](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5547)
  - quote page error unhandled fetch error rwr fusion (BZNext.js)
    [BZFE-5554](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5554)
  - fix amp detection (BZNext.js)
    [BZFE-5559](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5559)
  - DATA Updates content server to handle missing cannonical path (BZNext.js)
    [BZFE-5566](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5566)
  - primary image not showing on amp after last update next (BZNext.js)
    [BZFE-5572](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5572)
  - fix benzinga briefs loading (BZNext.js)
    [BZFE-5597](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5597)
  - fix amp styles (BZNext.js)
    [BZFE-5605](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5605)
  - fix stock movers spacing issues on mobile (BZNext.js)
    [BZFE-5609](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5609)
  - fix case sensitive alternative investments offerings filter (BZNext.js)
    [BZFE-5613](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5613)
  - default image for broken images (BZNext.js)
    [BZFE-5615](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5615)
  - analytics custom data reporting issue next (BZNext.js)
    [BZFE-5635](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5635)
  - top stories parse ly block should use benzinga images next (BZNext.js)
    [BZFE-5637](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5637)
  - article error style prop expects a mapping from style next (BZNext.js)
    [BZFE-5647](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5647)
  - fix error link is a void element tag next (BZNext.js)
    [BZFE-5652](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5652)
  - warning invalid dom property charset did you mean charset (BZNext.js)
    [BZFE-5675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5675)
  - stying disappears from quotes key stats next (BZNext.js)
    [BZFE-5681](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5681)
  - fill out missing articles (BZNext.js)
    [BZFE-5685](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5685)
  - missing check for article meta next (BZNext.js)
    [BZFE-5691](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5691)
  - article page minified react error 62 next (BZNext.js)
    [BZFE-5692](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5692)
  - fix missing article 404 page (BZNext.js)
    [BZFE-5697](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5697)
  - error rendered more hooks than during the previous render (BZNext.js)
    [BZFE-5728](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5728)
  - fix chart timerange styling (BZNext.js)
    [BZFE-5729](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5729)
  - fix missing image draft crash (BZNext.js)
    [BZFE-5732](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5732)
  - fix load popular stories on home page (BZNext.js)
    [BZFE-5734](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5734)
  - update route to node redirect (BZNext.js)
    [BZFE-5741](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5741)
  - fix alt investing news block (BZNext.js)
    [BZFE-5758](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5758)
  - bz image optimizer seems to be broken (BZNext.js)
    [BZFE-5761](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5761)
  - fix contentfeed infinite react loop (BZNext.js)
    [BZFE-5815](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5815)
  - fix missing image crash (BZNext.js)
    [BZFE-5823](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5823)
  - fix infinite load loop on content feed (BZNext.js)
    [BZFE-5824](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5824)
  - fix popular load more (BZNext.js)
    [BZFE-5825](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5825)
  - fix advertising menu overlay (BZNext.js)
    [BZFE-5833](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5833)
  - fixes to alts page mobile layout (BZNext.js)
    [BZFE-5834](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5834)
  - broken author link fallback (BZNext.js)
    [BZFE-5856](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5856)
  - fix component width issues (BZNext.js)
    [BZFE-5859](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5859)
  - fix quotes page issues (BZNext.js)
    [BZFE-5883](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5883)
  - broken layout on quote card (BZNext.js)
    [BZFE-5884](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5884)
  - quotes page news tab content width issue (BZNext.js)
    [BZFE-5886](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5886)
  - trade idea share link duplicated (BZNext.js)
    [BZFE-5891](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5891)
  - fix feed button observer (BZNext.js)
    [BZFE-5895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5895)
  - article pages share to email not working (BZNext.js)
    [BZFE-5897](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5897)
  - fix overlay issue w css (BZNext.js)
    [BZFE-5917](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5917)
  - call to action use vertical layout (BZNext.js)
    [BZFE-5918](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5918)
  - fix get image (BZNext.js)
    [BZFE-5926](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5926)
  - fix broken isoffers (BZNext.js)
    [BZFE-5931](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5931)
  - clean up article log (BZNext.js)
    [BZFE-5940](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5940)
  - debug mobile article layout issue (BZNext.js)
    [BZFE-5948](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5948)
  - wiims section on homepage showing old data (BZNext.js)
    [BZFE-5967](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5967)
  - fix sec filing landing page layout issue (BZNext.js)
    [BZFE-5983](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5983)
  - fix article metadata (BZNext.js)
    [BZFE-6027](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6027)
  - ad unit on crypto causing page to overflow (BZNext.js)
    [BZFE-6044](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6044)
  - debug missing alts (BZNext.js)
    [BZFE-6049](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6049)
  - auth refresh bug fix (Data.js)
    [DATA-5213](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5213)

## Task
  - setup storybook for widgets (BZNext.js)
    [BZFE-3708](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3708)
  - migrate generic topic pages (BZNext.js)
    [BZFE-4801](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4801)
  - modify stock movers table to use custom tickers (BZNext.js)
    [BZFE-4834](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4834)
  - finish calendar migrations to fusion (BZNext.js)
    [BZFE-4848](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4848)
  - wire up alt investing listing page (BZNext.js)
    [BZFE-4858](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4858)
  - cleanup page layout component (BZNext.js)
    [BZFE-4881](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4881)
  - Improve article author byline (.com) (BZNext.js)
    [BZFE-4882](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4882)
  - handle 404 for dynamic routes (BZNext.js)
    [BZFE-4894](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4894)
  - premium placement content block (BZNext.js)
    [BZFE-4905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4905)
  - replace exco unit w primis unit (BZNext.js)
    [BZFE-4935](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4935)
  - setup ag grid import modules correctly (BZNext.js)
    [BZFE-4941](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4941)
  - rotate dianomi mgid ads article template (BZNext.js)
    [BZFE-4945](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4945)
  - implement redesign of tool navigation (BZNext.js)
    [BZFE-4948](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4948)
  - add lines to the ads txt for primis (BZNext.js)
    [BZFE-4955](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4955)
  - add primis to amp page (BZNext.js)
    [BZFE-4958](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4958)
  - hardcode oneclick signal (BZNext.js)
    [BZFE-4961](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4961)
  - optimize webfonts for google pagespeed (BZNext.js)
    [BZFE-4975](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4975)
  - images should include heigh width (BZNext.js)
    [BZFE-4977](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4977)
  - update Ads.txt (BZNext.js)
    [BZFE-4985](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4985)
  - add taboola to articles including amp (BZNext.js)
    [BZFE-4994](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4994)
  - cleanup ads txt (BZNext.js)
    [BZFE-5010](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5010)
  - navigation bar style iterations (BZNext.js)
    [BZFE-5011](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5011)
  - in article ic ad units mobile desktop (BZNext.js)
    [BZFE-5013](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5013)
  - in article calendar widget (BZNext.js)
    [BZFE-5032](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5032)
  - Update Ads.txt - Primis (BZNext.js)
    [BZFE-5040](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5040)
  - add see all at end of calendar tabs that links to our list of calendars (BZNext.js)
    [BZFE-5047](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5047)
  - update ic ads txt 202112 (BZNext.js)
    [BZFE-5048](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5048)
  - update allotted ad unit sizes for home page (BZNext.js)
    [BZFE-5049](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5049)
  - link calendar seo content (BZNext.js)
    [BZFE-5060](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5060)
  - update search styling for mobile view to match next (BZNext.js)
    [BZFE-5063](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5063)
  - place fixed sponsored content to homepage (BZNext.js)
    [BZFE-5072](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5072)
  - benzinga briefs on homepage (BZNext.js)
    [BZFE-5076](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5076)
  - add sponsored content to top of quotes news feed (BZNext.js)
    [BZFE-5077](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5077)
  - Update Ads.txt - Ampliffy (BZNext.js)
    [BZFE-5040](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5040)
  - quote news tab category should collapse by default on mobile (BZNext.js)
    [BZFE-5080](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5080)
  - quotes news tab category filter should send no channels when all filters selected (BZNext.js)
    [BZFE-5083](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5083)
  - mobile search ux update (BZNext.js)
    [BZFE-5086](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5086)
  - fix missing submit button trade ideas (BZNext.js)
    [BZFE-5087](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5087)
  - add delete option to trade ideas (BZNext.js)
    [BZFE-5088](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5088)
  - remove timestamp from sponsored content news (BZNext.js)
    [BZFE-5089](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5089)
  - fusion ci cd should check changelog first (BZNext.js)
    [BZFE-5092](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5092)
  - update loading button style on calendar pages (BZNext.js)
    [BZFE-5094](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5094)
  - finalize headless cms article layout (BZNext.js)
    [BZFE-5097](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5097)
  - update ads txt optimize ampliffy (BZNext.js)
    [BZFE-5099](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5099)
  - update fixed sponsored post (BZNext.js)
    [BZFE-5108](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5108)
  - data brought to you by benzinga apis on calendars (BZNext.js)
    [BZFE-5110](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5110)
  - fix login wrong password error handling (BZNext.js)
    [BZFE-5117](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5117)
  - update benzinga briefs section to options on home page (BZNext.js)
    [BZFE-5120](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5120)
  - add image meta to benzinga com homepage (BZNext.js)
    [BZFE-5122](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5122)
  - updates to ad layout (BZNext.js)
    [BZFE-5123](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5123)
  - move offerings data to server side props (BZNext.js)
    [BZFE-5125](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5125)
  - update robots txt with new sitemaps (BZNext.js)
    [BZFE-5126](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5126)
  - add tools to mobile menu (BZNext.js)
    [BZFE-5131](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5131)
  - mobile menu style fixes (BZNext.js)
    [BZFE-5132](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5132)
  - optimize crypto cannabis landing pages (BZNext.js)
    [BZFE-5133](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5133)
  - fix briefs showing no results (BZNext.js)
    [BZFE-5134](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5134)
  - setup content server w bzql expression library (BZNext.js)
    [BZFE-5138](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5138)
  - add ipo nft money content to home page (BZNext.js)
    [BZFE-5146](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5146)
  - add see all on benzinga briefs (BZNext.js)
    [BZFE-5153](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5153)
  - rotating sponsored content (BZNext.js)
    [BZFE-5154](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5154)
  - remove document dependency on hubspot token (BZNext.js)
    [BZFE-5161](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5161)
  - benzinga briefs news block (BZNext.js)
    [BZFE-5163](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5163)
  - fixing watchlists movers quote outing bug (BZNext.js)
    [BZFE-5164](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5164)
  - setup topic pages w content api from cms (BZNext.js)
    [BZFE-5166](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5166)
  - key points block (BZNext.js)
    [BZFE-5168](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5168)
  - lazy load ads (BZNext.js)
    [BZFE-5172](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5172)
  - fix dupliate article footer (BZNext.js)
    [BZFE-5177](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5177)
  - Removes old sitemaps no longer needed (BZNext.js)
    [BZFE-5179](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5179)
  - update robots txt with new sitemaps next ongoing (BZNext.js)
    [BZFE-5179](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5179)
  - AutoDeploy (BZNext.js)
    [BZFE-5192](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5192)
  - hide trade ideas in articles for now (BZNext.js)
    [BZFE-5199](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5199)
  - setup developer session environment for bznext (BZNext.js)
    [BZFE-5200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5200)
  - fix stock movers company name height (BZNext.js)
    [BZFE-5202](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5202)
  - remove benzinga partner in by line for sponsored content (BZNext.js)
    [BZFE-5204](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5204)
  - fix og meta image for article pages (BZNext.js)
    [BZFE-5208](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5208)
  - add ads news feed to quote analyst ratings page (BZNext.js)
    [BZFE-5209](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5209)
  - add secondary ad unit to home page (BZNext.js)
    [BZFE-5212](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5212)
  - article bullet point style update (BZNext.js)
    [BZFE-5215](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5215)
  - update default styles for tables (BZNext.js)
    [BZFE-5218](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5218)
  - migrate content api to use cached route (BZNext.js)
    [BZFE-5230](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5230)
  - add crypto name from description (BZNext.js)
    [BZFE-5233](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5233)
  - fix offerings endpoint (BZNext.js)
    [BZFE-5247](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5247)
  - configure articles pages for editors to fetch data from draft api (BZNext.js)
    [BZFE-5249](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5249)
  - add featured offerings to offerings articles (BZNext.js)
    [BZFE-5251](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5251)
  - alternative investments listing card style issue (BZNext.js)
    [BZFE-5254](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5254)
  - redesign 404 error (BZNext.js)
    [BZFE-5255](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5255)
  - prevent duplicate news on quotes pages (BZNext.js)
    [BZFE-5257](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5257)
  - onboarding ux iterations (BZNext.js)
    [BZFE-5261](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5261)
  - update bz track to include label property (BZNext.js)
    [BZFE-5263](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5263)
  - debug and improve nextjs node heap errors (BZNext.js)
    [BZFE-5264](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5264)
  - Increase node max heap size -> 8192 (BZNext.js)
    [BZFE-5264](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5264)
  - update homepage to use new news api (BZNext.js)
    [BZFE-5271](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5271)
  - handle alt investing ui ux feedback (BZNext.js)
    [BZFE-5282](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5282)
  - advertiser disclosure tooltip text update for new template (BZNext.js)
    [BZFE-5295](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5295)
  - sort tweet tickers by primary first (BZNext.js)
    [BZFE-5299](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5299)
  - updated thumbnail size (BZNext.js)
    [BZFE-5300](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5300)
  - cleanup analyst news request quotes routing (BZNext.js)
    [BZFE-5302](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5302)
  - improve quote header for seo next (BZNext.js)
    [BZFE-5317](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5317)
  - test money page at root route (BZNext.js)
    [BZFE-5320](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5320)
  - improve quotes page seo focus analyst ratings (BZNext.js)
    [BZFE-5321](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5321)
  - my account dropdown (BZNext.js)
    [BZFE-5325](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5325)
  - user login flow improvements (BZNext.js)
    [BZFE-5326](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5326)
  - handle json error in layout api (BZNext.js)
    [BZFE-5327](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5327)
  - handle ogimage replace error (BZNext.js)
    [BZFE-5328](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5328)
  - flipped color tab bar (BZNext.js)
    [BZFE-5341](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5341)
  - create stock quotes block (BZNext.js)
    [BZFE-5344](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5344)
  - add image gallery block component (BZNext.js)
    [BZFE-5345](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5345)
  - setup alternative investments page seo (BZNext.js)
    [BZFE-5348](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5348)
  - fix mobile layout on alternatives page for filters (BZNext.js)
    [BZFE-5349](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5349)
  - lazy load primis on 1st scroll next (BZNext.js)
    [BZFE-5359](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5359)
  - spike out new app dotcom and the article page (BZNext.js)
    [BZFE-5361](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5361)
  - lazy load dynamic loading for taboola next (BZNext.js)
    [BZFE-5363](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5363)
  - deploy bzimage component (BZNext.js)
    [BZFE-5364](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5364)
  - lazy load article tickers next (BZNext.js)
    [BZFE-5366](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5366)
  - fix font font load issues for article titles next (BZNext.js)
    [BZFE-5367](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5367)
  - low hanging fruit article cleanup (BZNext.js)
    [BZFE-5370](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5370)
  - add pro links to short interest page (BZNext.js)
    [BZFE-5374](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5374)
  - implement different primis video player tags depending on the section of the site that it (BZNext.js)
    [BZFE-5375](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5375)
  - fix ticker layout on press releases (BZNext.js)
    [BZFE-5380](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5380)
  - build table of contents component (BZNext.js)
    [BZFE-5391](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5391)
  - article page remove components (BZNext.js)
    [BZFE-5397](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5397)
  - add gam ad manager custom targeting for all pages next (BZNext.js)
    [BZFE-5398](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5398)
  - add the bz channel analyst ratings tag to the quotes analyst ratings page (BZNext.js)
    [BZFE-5406](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5406)
  - article package (BZNext.js)
    [BZFE-5409](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5409)
  - Improves cache time for article pages (BZNext.js)
    [BZFE-5411](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5411)
  - Adds analyze to package.json, enables swcMinify (BZNext.js)
    [BZFE-5416](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5416)
  - replace moment js usage with luxon next (BZNext.js)
    [BZFE-5422](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5422)
  - faq component section answers misspelled in title (BZNext.js)
    [BZFE-5439](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5439)
  - Sets up a test page (/testpage) to check a primis live stream (BZNext.js)
    [BZFE-5450](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5450)
  - create a basic component for a news (BZNext.js)
    [BZFE-5468](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5468)
  - integrate newsblock component (BZNext.js)
    [BZFE-5469](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5469)
  - related article images for amp non amp aritcle should use bzimage lazy load next (BZNext.js)
    [BZFE-5470](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5470)
  - footer amp non amp app images should use bzimage lazy load next (BZNext.js)
    [BZFE-5471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5471)
  - Integrated Datadog RUM analytics (BZNext.js)
    [BZFE-5472](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5472)
  - kevins favorites section kevin can add his own copy (BZNext.js)
    [BZFE-5478](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5478)
  - add perks button to quote pages (BZNext.js)
    [BZFE-5480](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5480)
  - Updates Cision news block title (BZNext.js)
    [BZFE-5481](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5481)
  - add default background image for post card (BZNext.js)
    [BZFE-5484](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5484)
  - handle antd js filesize (BZNext.js)
    [BZFE-5488](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5488)
  - follow up from resolve related article images for amp non amp aritcle should use bzimage lazy (BZNext.js)
    [BZFE-5491](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5491)
  - additional quote page ad units (BZNext.js)
    [BZFE-5492](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5492)
  - run datadog rum on more pages next (BZNext.js)
    [BZFE-5494](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5494)
  - improve primis js load on amp mobile pages (BZNext.js)
    [BZFE-5498](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5498)
  - migrate article content parsing to server side (BZNext.js)
    [BZFE-5499](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5499)
  - migrate crypto quote card (BZNext.js)
    [BZFE-5515](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5515)
  - migrate standout card (BZNext.js)
    [BZFE-5516](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5516)
  - migrate star rating block (BZNext.js)
    [BZFE-5517](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5517)
  - migrate yoast how to block (BZNext.js)
    [BZFE-5518](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5518)
  - money breadcrumbs should be ssr (BZNext.js)
    [BZFE-5525](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5525)
  - update perks url (BZNext.js)
    [BZFE-5528](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5528)
  - money template loading fixes (BZNext.js)
    [BZFE-5531](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5531)
  - add article page autorefresh trigger (BZNext.js)
    [BZFE-5533](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5533)
  - remove moment js in favor of luxon (BZNext.js)
    [BZFE-5534](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5534)
  - Fixes 404 wording, updates svg asset (BZNext.js)
    [BZFE-5538](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5538)
  - money post ui ux fixes (BZNext.js)
    [BZFE-5541](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5541)
  - fix image height widget for money components (BZNext.js)
    [BZFE-5542](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5542)
  - fix article meta data issue (BZNext.js)
    [BZFE-5544](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5544)
  - change ad disclosure to css content (BZNext.js)
    [BZFE-5545](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5545)
  - fix css import for aggrid (BZNext.js)
    [BZFE-5546](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5546)
  - implement primis player on quote pages (BZNext.js)
    [BZFE-5550](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5550)
  - migrate ag grid css imports to component (BZNext.js)
    [BZFE-5551](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5551)
  - Lazily loads addthis sharebar (BZNext.js)
    [BZFE-5556](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5556)
  - standalone money app (BZNext.js)
    [BZFE-5558](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5558)
  - fine tune standout card css (BZNext.js)
    [BZFE-5563](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5563)
  - update stock movers for wide layout (BZNext.js)
    [BZFE-5574](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5574)
  - single quote block layout (BZNext.js)
    [BZFE-5575](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5575)
  - BZFE Fixes article reloads and cleanup some logs (BZNext.js)
    [BZFE-5576](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5576)
  - add the bz channel earnings tag to the quotes page earnings tab (BZNext.js)
    [BZFE-5577](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5577)
  - homepage tabs trending briefs recent (BZNext.js)
    [BZFE-5581](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5581)
  - bullets above image mobile only (BZNext.js)
    [BZFE-5582](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5582)
  - author date time on one line mobile only (BZNext.js)
    [BZFE-5583](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5583)
  - remove ui package utils directory (BZNext.js)
    [BZFE-5584](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5584)
  - fixes to alts page (BZNext.js)
    [BZFE-5586](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5586)
  - update home page newsletter hubspot form id (BZNext.js)
    [BZFE-5589](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5589)
  - home page exclusives carousel (BZNext.js)
    [BZFE-5596](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5596)
  - fix benzingabriefs component data not loading (BZNext.js)
    [BZFE-5598](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5598)
  - setup sidebar box with select articles (BZNext.js)
    [BZFE-5599](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5599)
  - home page tabs style change (BZNext.js)
    [BZFE-5601](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5601)
  - remove primis video temporarily (BZNext.js)
    [BZFE-5602](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5602)
  - briefs tab add yellow shading to new items (BZNext.js)
    [BZFE-5603](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5603)
  - optimize alternative offering w bzimage (BZNext.js)
    [BZFE-5610](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5610)
  - fix top story images not loading in the sidebar (BZNext.js)
    [BZFE-5611](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5611)
  - fix ticker primis ids (BZNext.js)
    [BZFE-5612](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5612)
  - the author profile in this article currently leads to a 404 page (BZNext.js)
    [BZFE-5627](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5627)
  - add facebook app id to metadata (BZNext.js)
    [BZFE-5628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5628)
  - next js article pages say contributor rather than staff writer (BZNext.js)
    [BZFE-5631](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5631)
  - fix stock movers results (BZNext.js)
    [BZFE-5643](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5643)
  - add cannabis to ic zones (BZNext.js)
    [BZFE-5644](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5644)
  - fixes to seo meta data (BZNext.js)
    [BZFE-5648](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5648)
  - refactor footer to not use antd components (BZNext.js)
    [BZFE-5650](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5650)
  - remove trade ideas from home page (BZNext.js)
    [BZFE-5657](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5657)
  - cwv fixes to various article layouts (BZNext.js)
    [BZFE-5658](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5658)
  - events and exclusives should be using the same carousel (BZNext.js)
    [BZFE-5662](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5662)
  - eliminate addthis share dependency for optimization purposes (BZNext.js)
    [BZFE-5665](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5665)
  - sort platform offerings by newest first (BZNext.js)
    [BZFE-5669](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5669)
  - setup psychedelics zone for ic units (BZNext.js)
    [BZFE-5672](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5672)
  - fix typeerror jsxdevruntime jsxdev is not a function (BZNext.js)
    [BZFE-5676](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5676)
  - debug published date discrepency (BZNext.js)
    [BZFE-5679](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5679)
  - debug sorted search results sorting (BZNext.js)
    [BZFE-5680](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5680)
  - add meta tag content properties (BZNext.js)
    [BZFE-5684](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5684)
  - add email capture for amp pages (BZNext.js)
    [BZFE-5688](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5688)
  - alt company page (BZNext.js)
    [BZFE-5698](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5698)
  - digital securities add to menu under markets (BZNext.js)
    [BZFE-5700](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5700)
  - fix link for sponsored content on quotes pages (BZNext.js)
    [BZFE-5706](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5706)
  - remove briefs tab (BZNext.js)
    [BZFE-5714](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5714)
  - fix amp layout issue (BZNext.js)
    [BZFE-5717](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5717)
  - fix author layout for large mobile widths (BZNext.js)
    [BZFE-5723](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5723)
  - hide addthis on single article (BZNext.js)
    [BZFE-5724](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5724)
  - hide addthis on single article change (BZNext.js)
    [BZFE-5724](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5724)
  - BZFE Improves cache times for briefs recent (BZNext.js)
    [BZFE-5733](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5733)
  - add event tracking related articles on benzinga com (BZNext.js)
    [BZFE-5735](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5735)
  - fix article table styling (BZNext.js)
    [BZFE-5740](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5740)
  - fix post feed image sizing (BZNext.js)
    [BZFE-5742](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5742)
  - channel vertical sponsorship template (BZNext.js)
    [BZFE-5748](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5748)
  - follow up from bug bzfe 5729 fix chart timerange styling (BZNext.js)
    [BZFE-5751](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5751)
  - update alternative investments news feed w content feed (BZNext.js)
    [BZFE-5752](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5752)
  - fix missing amp image bug (BZNext.js)
    [BZFE-5753](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5753)
  - follow up from task bzfe 5662 events and exclusives should be using the same carousel (BZNext.js)
    [BZFE-5754](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5754)
  - update listingpreivew component for art offerings (BZNext.js)
    [BZFE-5763](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5763)
  - update recent news feed to use contentfeed server (BZNext.js)
    [BZFE-5764](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5764)
  - update cannabis menu with additional options (BZNext.js)
    [BZFE-5770](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5770)
  - ignore rum errors for gsi logger origin (BZNext.js)
    [BZFE-5773](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5773)
  - integrate seo title into article meta data (BZNext.js)
    [BZFE-5774](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5774)
  - compare products sidebyside block (BZNext.js)
    [BZFE-5786](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5786)
  - add featuredreviewblock to next (BZNext.js)
    [BZFE-5787](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5787)
  - add missing testimonial component block (BZNext.js)
    [BZFE-5788](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5788)
  - setup listingpreview component w block (BZNext.js)
    [BZFE-5789](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5789)
  - add missing youtube embed component block (BZNext.js)
    [BZFE-5792](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5792)
  - add missing core columns component block (BZNext.js)
    [BZFE-5793](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5793)
  - add createsession helper based on node env (BZNext.js)
    [BZFE-5801](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5801)
  - integration call to action block (BZNext.js)
    [BZFE-5812](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5812)
  - adjust ad display for mobile desktop (BZNext.js)
    [BZFE-5826](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5826)
  - fix dfp tags (BZNext.js)
    [BZFE-5840](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5840)
  - optimize fa brand icons (BZNext.js)
    [BZFE-5847](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5847)
  - updated content layout for landing listing article pages (BZNext.js)
    [BZFE-5849](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5849)
  - set fixed height for share bar next (BZNext.js)
    [BZFE-5851](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5851)
  - implement twitter instagram embed (BZNext.js)
    [BZFE-5860](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5860)
  - add youtube list block component (BZNext.js)
    [BZFE-5862](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5862)
  - add missing team block component (BZNext.js)
    [BZFE-5863](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5863)
  - add missing quote block (BZNext.js)
    [BZFE-5864](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5864)
  - small fixes (BZNext.js)
    [BZFE-5865](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5865)
  - reduce article js footprint (BZNext.js)
    [BZFE-5866](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5866)
  - offering fully fundedlabel company offerings sort (BZNext.js)
    [BZFE-5870](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5870)
  - move getauthor to data package add local cache next (BZNext.js)
    [BZFE-5872](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5872)
  - fix image gallery height layout (BZNext.js)
    [BZFE-5874](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5874)
  - follow up from task bzfe 5688 add email capture for amp pages (BZNext.js)
    [BZFE-5878](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5878)
  - fix cta text free trial now get free trial (BZNext.js)
    [BZFE-5879](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5879)
  - setup routing for money pages (BZNext.js)
    [BZFE-5880](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5880)
  - remove find out more about sponsored posts on sponsored content pages (BZNext.js)
    [BZFE-5881](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5881)
  - fix article image placement size (BZNext.js)
    [BZFE-5882](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5882)
  - implement stock picks (BZNext.js)
    [BZFE-5893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5893)
  - debug scroll overlay issue (BZNext.js)
    [BZFE-5900](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5900)
  - add footer to money (BZNext.js)
    [BZFE-5903](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5903)
  - fix coreelements block wrapper (BZNext.js)
    [BZFE-5905](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5905)
  - fixes to share button layout (BZNext.js)
    [BZFE-5906](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5906)
  - add missing verified by (BZNext.js)
    [BZFE-5907](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5907)
  - follow up from bug bzfe 5897 article pages share to email not working (BZNext.js)
    [BZFE-5908](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5908)
  - add alts to main menu (BZNext.js)
    [BZFE-5913](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5913)
  - setup compact offering card (BZNext.js)
    [BZFE-5920](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5920)
  - debug missing table styles (BZNext.js)
    [BZFE-5925](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5925)
  - update all fortaweseome imports to reference icon directly (BZNext.js)
    [BZFE-5929](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5929)
  - add time cache timebreaker to requests (BZNext.js)
    [BZFE-5935](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5935)
  - bug w quotes scheduled earnings faq (BZNext.js)
    [BZFE-5941](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5941)
  - fix meta description w incorrect encoding next (BZNext.js)
    [BZFE-5945](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5945)
  - fix missing number styles w list (BZNext.js)
    [BZFE-5947](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5947)
  - clean console logs (BZNext.js)
    [BZFE-5953](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5953)
  - add stale while revalidate headers next (BZNext.js)
    [BZFE-5960](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5960)
  - implement native ic ad (BZNext.js)
    [BZFE-5961](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5961)
  - add author url to article schema next (BZNext.js)
    [BZFE-5964](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5964)
  - add follow on google news button to right of share buttons on articles (BZNext.js)
    [BZFE-5971](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5971)
  - hide watchlist on crypto sidebar (BZNext.js)
    [BZFE-5972](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5972)
  - copy changes in the key stats section of the quote page (BZNext.js)
    [BZFE-5974](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5974)
  - follow up from task bzfe 5971 add follow on google news button to right of share buttons on (BZNext.js)
    [BZFE-5975](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5975)
  - fix share button spacing issue (BZNext.js)
    [BZFE-5976](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5976)
  - setup watchlist iframe (BZNext.js)
    [BZFE-5985](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5985)
  - fix analyst ratings breadcrumbs (BZNext.js)
    [BZFE-5987](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5987)
  - page layout administration (BZNext.js)
    [BZFE-5988](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5988)
  - entity card w default wide compact layout (BZNext.js)
    [BZFE-5989](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5989)
  - link out to main calendar tools from quotes ratings dividends earnings guidance ideas short (BZNext.js)
    [BZFE-6016](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6016)
  - add crypto quote news (BZNext.js)
    [BZFE-6022](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6022)
  - add campaign integrations to template page (BZNext.js)
    [BZFE-6030](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6030)
  - meta console log cleanup next (BZNext.js)
    [BZFE-6064](https://gitlab.benzinga.io/benzinga/fusion/-/issues/6064)
  - remove token from refresh request (Data.js)
    [DATA-5017](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5017)
  - setup offerings api in data package (Data.js)
    [DATA-5250](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5250)
  - optimize and cleanup data package for benzinganext inclusion (Data.js)
    [Data-5402](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5402)
  - define unexpected any types in ui package (UI.js)
    [UI-4379](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4379)
  - create watchlists box style fixes (UI.js)
    [UI-4901](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4901)
  - create filter offerings landing page (UI.js)
    [UI-4922](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4922)
  - trade ideas report popup (UI.js)
    [UI-4992](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4992)
  - benzinga briefs block (UI.js)
    [UI-5008](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5008)
  - update mobile quotes layout (UI.js)
    [UI-5041](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5041)
  - update offerings design (UI.js)
    [UI-5042](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5042)
  - mobile tools css fixs (UI.js)
    [UI-5044](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5044)
  - fix missing submit button trade ideas (BZNext.js)
    [BZFE-5087](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5087)
  - cleanup fusion (UI.js)
    [UI-5653](https://gitlab.benzinga.io/benzinga/fusion/-/issues/5653)

# v2.9.6

## Feature
  - updated bznext to use data package for quotes (BZNext.js)
    [BZFE-3692](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3692)
  - add short interest as a data point to quotes page (BZNext.js)
    [BZFE-4070](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4070)
  - add related content to amp article pages (BZNext.js)
    [BZFE-4157](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4157)
  - fda calendar today tab (BZNext.js)
    [BZFE-4200](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4200)
  - the recent tab on the homepage should be it s own route (BZNext.js)
    [BZFE-4352](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4352)
  - add sec filing articles to site (BZNext.js)
    [BZFE-4481](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4481)
  - add trade ideas to quotes (BZNext.js)
    [BZFE-4544](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4544)
  - quote page seo (BZNext.js)
    [BZFE-4546](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4546)
  - pressrelease and seclanding pages (BZNext.js)
    [BZFE-4558](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4558)
  - events display on home page (BZNext.js)
    [BZFE-4580](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4580)
  - migrate scanner api to data package (BZNext.js)
    [BZFE-4599](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4599)
  - crypto page (BZNext.js)
    [BZFE-4629](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4629)
  - working article pages next (BZNext.js)
    [BZFE-4640](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4640)
  - create related articles service (BZNext.js)
    [BZFE-4641](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4641)
  - sidebar template for pressrelease articles (BZNext.js)
    [BZFE-4653](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4653)
  - home page ibrk compare table (BZNext.js)
    [BZFE-4671](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4671)
  - add ex co on amp (BZNext.js)
    [BZFE-4734](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4734)
  - tickerize content integration (BZNext.js)
    [BZFE-4799](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4799)
  - sidebar menu block (UI.js)
    [UI-4589](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4589)
  - simple movers widget touchups (UI.js)
    [UI-4729](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4729)

## Bug
  - calendar widget should be responsive (BZNext.js)
    [BZFE-4304](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4304)
  - amp html tag has an invalid layout specified by its attributes (BZNext.js)
    [BZFE-4418](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4418)
  - home page style fixes (BZNext.js)
    [BZFE-4471](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4471)
  - Fixes next custom routes, headers, public file locations (BZNext.js)
    [BZFE-4503](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4503)
  - bug earnings calendar surprise calc (BZNext.js)
    [BZFE-4514](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4514)
  - fix aggrid startup errors next (BZNext.js)
    [BZFE-4524](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4524)
  - fix public paths after deployment changes (BZNext.js)
    [BZFE-4539](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4539)
  - class override issue styled components (BZNext.js)
    [BZFE-4557](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4557)
  - fix watchlist movers (BZNext.js)
    [BZFE-4584](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4584)
  - errors box lint error (BZNext.js)
    [BZFE-4628](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4628)
  - fix preload warning next (BZNext.js)
    [bzfe-4636](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4636)
  - Fixes mobile header enlarged margin (BZNext.js)
    [BZFE-4645](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4645)
  - cannabis page event bug (BZNext.js)
    [BZFE-4654](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4654)
  - detected container element removed from dom (BZNext.js)
    [BZFE-4674](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4674)
  - fix home page secondary featured stories (BZNext.js)
    [BZFE-4679](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4679)
  - moon or bust cards list style issue (BZNext.js)
    [BZFE-4682](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4682)
  - quotes page has unclickable elements with a reduced page width (BZNext.js)
    [BZFE-4685](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4685)
  - Updates env var to choose correct segment analytics key (BZNext.js)
    [BZFE-4701](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4701)
  - sponsored content not loading issue (BZNext.js)
    [BZFE-4741](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4741)
  - clicking quote tabs does not include ticker in url (BZNext.js)
    [BZFE-4764](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4764)
  - fix login not returning (BZNext.js)
    [BZFE-4790](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4790)
  - next article pages bug fixes (BZNext.js)
    [BZFE-4837](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4837)
  - fix sec filing pages widtch broke (BZNext.js)
    [BZFE-4873](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4873)
  - fix next article pages mobile image resolution (BZNext.js)
    [BZFE-4884](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4884)
  - exco units are not loading on next article pages (BZNext.js)
    [BZFE-4886](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4886)
  - fix hubspot workflow welcome page (BZNext.js)
    [BZFE-4893](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4893)
  - fix application authentication workflow (BZNext.js)
    [BZFE-4924](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4924)
  - fix editor bar layout on mobile (BZNext.js)
    [BZFE-4925](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4925)
  - ui package lint error (UI.js)
    [UI-4681](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4681)
  - ui money library module not found for compareproducttable (UI.js)
    [UI-4700](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4700)
  - fix menu width on smaller screens (UI.js)
    [UI-4724](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4724)
  - fix missing top bar menu items (UI.js)
    [UI-4786](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4786)

## Task
  - analyze bundles for nextjs (BZNext.js)
    [BZFE-3649](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3649)
  - migrate news feed to new quotes page (BZNext.js)
    [BZFE-3734](https://gitlab.benzinga.io/benzinga/fusion/-/issues/3734)
  - integrate types safeawait for home data (BZNext.js)
    [BZFE-4375](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4375)
  - fix search bar display article results (BZNext.js)
    [BZFE-4432](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4432)
  - add corporation to schema (BZNext.js)
    [BZFE-4479](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4479)
  - more fixes (BZNext.js)
    [BZFE-4494](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4494)
  - REMIXD Ads.txt file (BZNext.js)
    [BZFE-4506](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4506)
  - break down ui package (BZNext.js)
    [BZFE-4513](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4513)
  - medirom update of q a section on quotes page (BZNext.js)
    [BZFE-4534](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4534)
  - finalize mvp onboarding flow (BZNext.js)
    [BZFE-4545](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4545)
  - declare types on bz next pages (BZNext.js)
    [BZFE-4549](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4549)
  - top bar widget style fixes (BZNext.js)
    [BZFE-4583](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4583)
  - create dynamic channel pages from home page (BZNext.js)
    [BZFE-4587](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4587)
  - stock quote pages (BZNext.js)
    [BZFE-4608](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4608)
  - any clip on home page (BZNext.js)
    [BZFE-4609](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4609)
  - home page alternative investments section (BZNext.js)
    [BZFE-4610](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4610)
  - update ad txt primis (BZNext.js)
    [BZFE-4611](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4611)
  - cannabis page touchups (BZNext.js)
    [BZFE-4614](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4614)
  - anyclip script loading fix on home page (BZNext.js)
    [BZFE-4623](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4623)
  - anyclip live script (BZNext.js)
    [BZFE-4624](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4624)
  - ads cleanup (BZNext.js)
    [BZFE-4639](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4639)
  - Removes server.js.  No longer needed.  For now. (BZNext.js)
    [BZFE-4642](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4642)
  - Cleans old stuff (covid) (BZNext.js)
    [BZFE-4646](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4646)
  - seo new schemas (BZNext.js)
    [BZFE-4651](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4651)
  - options newsletter oneclick trades 48bytesn (BZNext.js)
    [BZFE-4669](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4669)
  - update ads txt for exco (BZNext.js)
    [BZFE-4677](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4677)
  - update short interest effective date (BZNext.js)
    [BZFE-4684](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4684)
  - disable broker widget on homepage next (BZNext.js)
    [BZFE-4694](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4694)
  - dedupe ag grid (BZNext.js)
    [BZFE-4705](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4705)
  - econ calendar surprise fix (BZNext.js)
    [BZFE-4711](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4711)
  - IC - Ads.txt updates (BZNext.js)
    [BZFE-4712](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4712)
  - migrate simple scanner protype (BZNext.js)
    [BZFE-4713](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4713)
  - otc markets ticker page errors (BZNext.js)
    [BZFE-4716](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4716)
  - add taboola to amp (BZNext.js)
    [BZFE-4725](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4725)
  - Update the ads.txt for Investing Channel (BZNext.js)
    [BZFE-4727](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4727)
  - fix news client (BZNext.js)
    [BZFE-4782](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4782)
  - finalize trade idea reporting (BZNext.js)
    [BZFE-4785](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4785)
  - short interest improvements (BZNext.js)
    [BZFE-4787](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4787)
  - integrate custom sidebars (BZNext.js)
    [BZFE-4800](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4800)
  - short interest page data filters (BZNext.js)
    [BZFE-4804](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4804)
  - article editorial toolbar (BZNext.js)
    [BZFE-4806](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4806)
  - optimize internal news request (BZNext.js)
    [BZFE-4813](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4813)
  - ic component (BZNext.js)
    [BZFE-4844](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4844)
  - update home page sidebar ad units (BZNext.js)
    [BZFE-4856](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4856)
  - sidebar cleanup (BZNext.js)
    [BZFE-4875](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4875)
  - add custom dimensions to next meta (BZNext.js)
    [BZFE-4885](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4885)
  - fix home page sidebar ad units (BZNext.js)
    [BZFE-4890](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4890)
  - add bz track to bz next (BZNext.js)
    [BZ-4565](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4565)
  - renamed getquote functions in quote manager (Data.js)
    [DATA-4195](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4195)
  - setup logo analytics data package api (Data.js)
    [DATA-4548](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4548)
  - add profile meta data into data package (Data.js)
    [DATA-4579](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4579)
  - create crypto api data package (Data.js)
    [DATA-4675](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4675)
  - fix chart using wrong key on production (Data.js)
    [Data.js-4196](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4196)
  - list traders tickers (UI.js)
    [UI-4259](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4259)
  - text link ad blocks cnn style (UI.js)
    [UI-4265](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4265)
  - create compact news block (UI.js)
    [UI-4398](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4398)
  - automate clips from youtube shows on home page (UI.js)
    [UI-4460](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4460)
  - new channels to news drop down (UI.js)
    [UI-4474](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4474)
  - add trading school to navigation (UI.js)
    [UI-4512](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4512)
  - breaking news banner fix (UI.js)
    [UI-4525](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4525)
  - tipping zing to yourself bug (UI.js)
    [UI-4535](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4535)
  - update trade ideas to use styled components (UI.js)
    [UI-4561](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4561)
  - news banner fixes (UI.js)
    [UI-4572](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4572)
  - fix faq alignment (UI.js)
    [UI-4574](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4574)
  - single watchlist users should not show dropdown (UI.js)
    [UI-4582](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4582)
  - related articles component ui (UI.js)
    [UI-4585](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4585)
  - replicate wp block components (UI.js)
    [UI-4588](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4588)
  - rename fontawesome custom (UI.js)
    [UI-4590](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4590)
  - sideswipe functionality (UI.js)
    [UI-4613](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4613)
  - create compare product table (UI.js)
    [UI-4631](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4631)
  - moon or bust compact variant (UI.js)
    [UI-4633](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4633)
  - migrate export function to fusion fda calendar (UI.js)
    [UI-4635](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4635)
  - migrate best stock components (UI.js)
    [UI-4648](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4648)
  - develop watch page components (UI.js)
    [UI-4660](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4660)
  - update crypto card data (UI.js)
    [UI-4676](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4676)
  - margin calculator (UI.js)
    [UI-4678](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4678)
  - moon or bust email modal style issue (UI.js)
    [UI-4680](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4680)
  - breaking news banner text style fix (UI.js)
    [UI-4697](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4697)
  - update auth modal (UI.js)
    [UI-4751](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4751)
  - migrate capital rate calculator (UI.js)
    [UI-4780](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4780)
  - migrate capital expense calculator (UI.js)
    [UI-4781](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4781)
  - money product card (UI.js)
    [UI-4818](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4818)
  - flat call to action card (UI.js)
    [UI-4819](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4819)
  - update nav bar on com with new items (UI.js)
    [UI-4828](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4828)
  - offerings block (UI.js)
    [UI-4829](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4829)
  - offerings filter box (UI.js)
    [UI-4830](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4830)
  - margin calculator iterations (UI.js)
    [UI-4832](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4832)
  - replicate crypto com widgets (UI.js)
    [UI-4838](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4838)
  - add share buttons to margin calculator short interest (UI.js)
    [UI-4849](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4849)
  - alternate version of offering card (UI.js)
    [UI-4859](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4859)
  - style fixes to top stories (UI.js)
    [UI-4895](https://gitlab.benzinga.io/benzinga/fusion/-/issues/4895)

