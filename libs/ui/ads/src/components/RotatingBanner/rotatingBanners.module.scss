.edge-logo {
  max-width: 254px;
  max-height: 27px;
  @media (max-width: 800px) {
    max-width: 140px;
    max-height: 20px;
  }
}

.earnings-webinar-v1 {
  background: linear-gradient(90deg, #040419 0%, #063274 100%);

  .banner {
    .banner-content > div {
      display: flex;
      flex-direction: column-reverse;
      height: 100%;
      align-items: flex-start;
      padding-right: 1rem;
      @media (max-width: 800px) {
        padding-right: 0.5rem;
      }
    }
    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-size: 26px;
      font-weight: 700;
      line-height: 35px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      // text-transform: uppercase;

      // @media (max-width: 1360px) {
      //   font-size: 28px;
      //   line-height: 28px;
      // }
      @media (max-width: 1443px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1270px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 14px;
      }
    }
    p {
      color: #3588E5;
      font-size: 16px;
      line-height: 16px;
      font-weight: 700;
      text-transform: uppercase;
      padding-bottom: 0.25rem;
      max-width: fit-content;
      font-family: Inter, sans-serif;
      letter-spacing: 0.18em;
      text-align: left;
      height: 100%;

      @media (max-width:870px) {
        font-size: 14px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
        letter-spacing: 0.1em;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #1A81FA;
      padding: 1rem !important;

      color: white;
      white-space: nowrap;
      text-transform: uppercase;
      border-radius: 2px !important;
      font-family: Inter, sans-serif;
      font-size: 20px;
      font-weight: 800;
      line-height: 26px;
      letter-spacing: 0.08em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      height: 80%;

      &:hover {
        background-color:#3a74da;
      }

      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 20px
      }

      @media(max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
        white-space: wrap;
        text-align: center;
        margin-left: 1rem;
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/erx-end-bg.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 944px;
    height: 80px;
  }
}

.edge-v18 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-v18-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;

      @media (max-width: 1350px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #479CFF;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        padding: 1.25rem 2rem !important;
        text-transform: uppercase;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          padding: 1.25rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.pro-sale-v4-july4-1 {
  background-color: #000000;
  background-image: url('/next-assets/images/banners/pro/pro-v4-july4-1.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 1rem;
      @media (max-width: 800px) {
        gap: 0.5rem;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 27px;
      line-height: 26px;
      font-weight: 400;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
      color: white;
      letter-spacing: 0.04em;

      div:first-child {
        font-size: 60px;
      }

      @media (max-width: 1140px) {
        font-size: 20px;
        line-height: 22px;
        div:first-child {
          font-size: 40px;
        }
      }

      @media (max-width: 900px) {
        font-size: 18px;
        gap: 0.5rem;
        div:first-child {
          font-size: 30px;
        }
      }
      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        font-size: 14px;
        line-height: 16px;
        div:first-child {
          font-size: 20px;
        }
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-size: 25px;
      font-weight: 400;
      color: #479CFF;
      line-height: 28px;
      letter-spacing: 0.2em;
      vertical-align: middle;
      text-transform: uppercase;
      white-space: wrap;
      max-width: 180px;
      position: relative;
      padding-right: 1rem;

      &::after {
        content: '';
        position: absolute;
        top: -12px;
        right: 0;
        width: 2px;
        height: 80px;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0) 0%,
          #479CFF 51.92%,
          rgba(0, 0, 0, 0) 100%
        );
      }

      @media (max-width: 1140px) {
        font-size: 21px;
        max-width: 160px;
      }

      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 16px;
        max-width: 70px;
        letter-spacing: 0.06em;
        &::after {
          top:-20px;
        }
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 24px;
        line-height: 26px;
        font-weight: 600;
        text-transform: uppercase;
        white-space: nowrap;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        letter-spacing: 0.1rem;
        padding: 1rem 2rem !important;

        background-color: #479CFF;
        border-radius: 0.75rem !important;
        box-shadow:
          2.67px 5.79px 9.97px 0px #FFFFFF40 inset,
          4px 4px 12px rgba(71, 156, 255, 0.6),
          4px 0 4px 4px rgba(155, 199, 250, -1) !important;


        @media (max-width: 1000px) {
          font-size: 20px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          padding: 0.5rem !important;
        }
      }
    }
  }
}

.pro-sale-v4-july4-2 {
  background-color: #000000;
  background-image: url('/next-assets/images/banners/pro/pro-v4-july4-2.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 1rem;
      @media (max-width: 800px) {
        gap: 0.5rem;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 32px;
      line-height: 26px;
      font-weight: 800;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.75rem;
      color: white;
      letter-spacing: 0.04em;

      em {
        padding-bottom: 0.25rem;
        position:relative;
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1.5px;
          background: linear-gradient(
            to right,
            rgba(0, 0, 0, 0) 0%,
            #FFFFFF 51.92%,
            rgba(0, 0, 0, 0) 100%
          );
        }
      }

      @media (max-width: 1330px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1220px) {
        font-size: 24px;
        line-height: 22px;
        gap: 0.5rem;
      }

      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        font-size: 14px;
        line-height: 16px;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-size: 12px;
      font-weight: 500;
      color: #D54343;
      line-height: 14px;
      letter-spacing: 0.12em;
      vertical-align: middle;
      text-align: center;
      text-transform: uppercase;
      white-space: wrap;
      max-width: 90px;
      position: relative;

      display: flex;
      flex-direction: column-reverse;
      align-items: center;
      gap: 0.25rem;
      padding-right: 0.5rem;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 2px;
        height: 80px;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0) 0%,
          #D54343 51.92%,
          rgba(0, 0, 0, 0) 100%
        );
      }

      @media (max-width: 800px) {
        font-size: 8px;
        line-height: 10px;
        letter-spacing: 0.06em;
        max-width: 60px;
      }
    }

    .percent-icon {
      width: 32px;
      height: 32px;
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 24px;
        line-height: 26px;
        font-weight: 600;
        text-transform: uppercase;
        white-space: nowrap;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        letter-spacing: 0.1rem;
        padding: 1rem 1.5rem !important;

        background: linear-gradient(90deg, #E65151 28.49%, #D54343 72.72%);
        border-radius: 0.75rem !important;
        box-shadow:
          0 0 0 1px rgba(255, 255, 255, 0.2),
          0 0 6px rgba(255, 255, 255, 0.6),
          0 0 12px rgba(255, 255, 255, 0.4) !important;


        @media (max-width: 1220px) {
          font-size: 20px;
          line-height: 22px;
        }

        @media (max-width: 800px) {
          font-size: 10px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          padding: 0.5rem !important;
        }
      }
    }
  }
}

.pro-sale-v4-july4-3 {
  background-color: #000000;
  background-image: url('/next-assets/images/banners/pro/pro-v4-july4-3.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 1rem;
      @media (max-width: 800px) {
        gap: 0.5rem;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 44px;
      line-height: 26px;
      font-weight: 800;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
      color: white;
      letter-spacing: 0.04em;

      @media (max-width: 1200px) {
        font-size: 36px;
      }

      @media (max-width: 1050px) {
        font-size: 32px;
      }

      @media (max-width: 930px) {
        font-size: 24px;
        line-height: 22px;
      }

      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        font-size: 18px;
        line-height: 16px;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-size: 16px;
      font-weight: 400;
      color: #D54343;
      line-height: 18px;
      letter-spacing: 0.4rem;
      text-transform: uppercase;
      white-space: wrap;
      position: relative;
      max-width: 260px;

      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 0.25rem;
      padding-right: 1rem;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 2px;
        height: 60px;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0) 0%,
          #D54343 51.92%,
          rgba(0, 0, 0, 0) 100%
        );
      }

      @media (max-width: 1050px) {
        font-size: 14px;
        letter-spacing: 0.2rem;
        max-width: 210px;
      }

      @media (max-width: 930px) {
        font-size: 12px;
        max-width: 160px;
        letter-spacing: 0.1rem;
      }

      @media (max-width: 800px) {
        flex-direction: column-reverse;
        font-size: 10px;
        line-height: 12px;
        max-width: 90px;
        text-align: center;
        letter-spacing: 0;
      }
    }

    .percent-icon {
      min-width: 60px;
      min-height: 60px;

      @media (max-width: 1050px) {
        min-width: 48px;
        min-height: 48px;
      }

      @media (max-width: 930px) {
        min-width: 32px;
        min-height: 32px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 24px;
        line-height: 26px;
        font-weight: 600;
        text-transform: uppercase;
        white-space: nowrap;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        letter-spacing: 0.1rem;
        padding: 1rem 1.5rem !important;

        background: linear-gradient(90deg, #E65151 28.49%, #D54343 72.72%);
        border-radius: 0.75rem !important;
        box-shadow:
          0 0 0 1px rgba(255, 255, 255, 0.2),
          0 0 6px rgba(255, 255, 255, 0.6),
          0 0 12px rgba(255, 255, 255, 0.4) !important;


        @media (max-width: 1220px) {
          font-size: 20px;
          line-height: 22px;
        }

        @media (max-width: 800px) {
          font-size: 10px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          padding: 0.5rem !important;
        }
      }
    }
  }
}

.pro-sale-v4-july4-4 {
  background-color: #000000;
  background-image: url('/next-assets/images/banners/pro/pro-v4-july4-4.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 1rem;
      @media (max-width: 800px) {
        gap: 0.5rem;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 24px;
      line-height: 22px;
      font-weight: 500;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.25rem;
      color: #479CFF;
      text-align: center;

      @media (max-width: 1330px) {
        font-size: 20px;
        line-height: 18px;
      }

      @media (max-width: 1130px) {
        font-size: 16px;
      }

      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 14px;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-size: 40px;
      font-weight: 700;
      color: white;
      line-height: 42px;
      white-space: nowrap;
      position: relative;
      padding-right: 1rem;
      text-align: center;

      &::after {
        content: '';
        position: absolute;
        top: -20px;
        right: 0;
        width: 2px;
        height: 80px;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0) 0%,
          #479CFF 51.92%,
          rgba(0, 0, 0, 0) 100%
        );
      }

      @media (max-width: 1330px) {
        font-size: 32px;
        line-height: 30px;
      }

      @media (max-width: 1130px) {
        font-size: 24px;
        line-height: 22px;
      }

      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 18px;
        white-space: wrap;
        max-width: 145px;
        padding-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 15px;
        line-height: 18px;
        font-weight: 600;
        text-transform: uppercase;
        white-space: nowrap;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        padding: 1rem !important;
        letter-spacing: 0.1rem;

        background: linear-gradient(90deg, #6BAFFE 28.49%, #479CFF 72.72%);
        border-radius: 0.75rem !important;
        box-shadow: 0px 1.83px 3.65px 0px #FFFFFF26 inset,
          0px 0.46px 1.83px 0px #2C6A9933 inset,
          0.91px 0.46px 0.46px 0px #FFFFFF6B inset,
          -0.46px -0.46px 1.46px 0px #FFFFFFB2 inset,
          0px 4.56px 4.2px 0px #FFFFFF3B inset,
          0px -5.48px 3.93px 0px #FFFFFF30 inset !important;

        @media (max-width: 800px) {
          font-size: 10px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          padding: 0.5rem !important;
          letter-spacing: 0;
        }
      }
    }
  }
}
